# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-25 16:58+0800\n"
"PO-Revision-Date: 2022-10-14 23:48+0200\n"
"Last-Translator: Rainshaw <<EMAIL>>\n"
"Language-Team: x_zhuo <<EMAIL>>\n"
"Language: zh-Hans \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: django_celery_beat/admin.py:60
msgid "Task (registered)"
msgstr "任务 (已注册的)"

#: django_celery_beat/admin.py:64
msgid "Task (custom)"
msgstr "任务 (自定义)"

#: django_celery_beat/admin.py:81
msgid "Need name of task"
msgstr "任务需要一个名称"

#: django_celery_beat/admin.py:87 django_celery_beat/models.py:618
msgid "Only one can be set, in expires and expire_seconds"
msgstr "不可以同时设置 expires 和 expire_seconds 字段"

#: django_celery_beat/admin.py:97
#, python-format
msgid "Unable to parse JSON: %s"
msgstr "无法解析 JSON: %s"

#: django_celery_beat/admin.py:125
#, fuzzy
#| msgid "Solar Schedule"
msgid "Schedule"
msgstr "日程时间表"

#: django_celery_beat/admin.py:130
#, fuzzy
#| msgid "Keyword Arguments"
msgid "Arguments"
msgstr "关键字参数"

#: django_celery_beat/admin.py:134
msgid "Execution Options"
msgstr ""

#: django_celery_beat/admin.py:179
#, python-brace-format
msgid "{0} task{1} {2} successfully {3}"
msgstr "{0} 任务{1} {2} 成功 {3}"

#: django_celery_beat/admin.py:182 django_celery_beat/admin.py:249
msgid "was,were"
msgstr "将"

#: django_celery_beat/admin.py:191
msgid "Enable selected tasks"
msgstr "启用选中的任务"

#: django_celery_beat/admin.py:197
msgid "Disable selected tasks"
msgstr "禁用选中的任务"

#: django_celery_beat/admin.py:209
msgid "Toggle activity of selected tasks"
msgstr "切换选中的任务"

#: django_celery_beat/admin.py:230
#, fuzzy, python-brace-format
#| msgid "task \"{0}\" not found"
msgid "task \"{not_found_task_name}\" not found"
msgstr "未找到\"{0}\"任务"

#: django_celery_beat/admin.py:246
#, python-brace-format
msgid "{0} task{1} {2} successfully run"
msgstr "{0} 任务{1} {2} 启动成功"

#: django_celery_beat/admin.py:252
msgid "Run selected tasks"
msgstr "运行选中的任务"

#: django_celery_beat/apps.py:13
msgid "Periodic Tasks"
msgstr "周期任务"

#: django_celery_beat/models.py:31
msgid "Days"
msgstr "天"

#: django_celery_beat/models.py:32
msgid "Hours"
msgstr "小时"

#: django_celery_beat/models.py:33
msgid "Minutes"
msgstr "分钟"

#: django_celery_beat/models.py:34
msgid "Seconds"
msgstr "秒"

#: django_celery_beat/models.py:35
msgid "Microseconds"
msgstr "毫秒"

#: django_celery_beat/models.py:39
msgid "Day"
msgstr "天"

#: django_celery_beat/models.py:40
msgid "Hour"
msgstr "小时"

#: django_celery_beat/models.py:41
msgid "Minute"
msgstr "分钟"

#: django_celery_beat/models.py:42
msgid "Second"
msgstr "秒"

#: django_celery_beat/models.py:43
msgid "Microsecond"
msgstr "毫秒"

#: django_celery_beat/models.py:47
msgid "Astronomical dawn"
msgstr "天文黎明"

#: django_celery_beat/models.py:48
msgid "Civil dawn"
msgstr "民事黎明"

#: django_celery_beat/models.py:49
msgid "Nautical dawn"
msgstr "航海黎明"

#: django_celery_beat/models.py:50
msgid "Astronomical dusk"
msgstr "天文黄昏"

#: django_celery_beat/models.py:51
msgid "Civil dusk"
msgstr "民事黄昏"

#: django_celery_beat/models.py:52
msgid "Nautical dusk"
msgstr "航海黄昏"

#: django_celery_beat/models.py:53
msgid "Solar noon"
msgstr "正午"

#: django_celery_beat/models.py:54
msgid "Sunrise"
msgstr "日出"

#: django_celery_beat/models.py:55
msgid "Sunset"
msgstr "日落"

#: django_celery_beat/models.py:89
msgid "Solar Event"
msgstr "日程事件"

#: django_celery_beat/models.py:90
msgid "The type of solar event when the job should run"
msgstr "当任务应该执行时的日程事件类型"

#: django_celery_beat/models.py:94
msgid "Latitude"
msgstr "纬度"

#: django_celery_beat/models.py:95
msgid "Run the task when the event happens at this latitude"
msgstr "当在此纬度发生事件时执行任务"

#: django_celery_beat/models.py:100
msgid "Longitude"
msgstr "经度"

#: django_celery_beat/models.py:101
msgid "Run the task when the event happens at this longitude"
msgstr "当在此经度发生事件时执行任务"

#: django_celery_beat/models.py:108
msgid "solar event"
msgstr "日程事件"

#: django_celery_beat/models.py:109
msgid "solar events"
msgstr "日程事件"

#: django_celery_beat/models.py:159
msgid "Number of Periods"
msgstr "周期数"

#: django_celery_beat/models.py:160
msgid "Number of interval periods to wait before running the task again"
msgstr "再次执行任务之前要等待的间隔周期数"

#: django_celery_beat/models.py:166
msgid "Interval Period"
msgstr "间隔周期"

#: django_celery_beat/models.py:167
msgid "The type of period between task runs (Example: days)"
msgstr "任务每次执行之间的时间间隔类型（例如：天）"

#: django_celery_beat/models.py:173
msgid "interval"
msgstr "间隔"

#: django_celery_beat/models.py:174
msgid "intervals"
msgstr "间隔"

#: django_celery_beat/models.py:201
msgid "every {}"
msgstr "每 {}"

#: django_celery_beat/models.py:206
msgid "every {} {}"
msgstr "每 {} {}"

#: django_celery_beat/models.py:217
msgid "Clock Time"
msgstr "定时时间"

#: django_celery_beat/models.py:218
msgid "Run the task at clocked time"
msgstr "在定时时间执行任务"

#: django_celery_beat/models.py:224 .\models.py:225
msgid "clocked"
msgstr "定时"

#: django_celery_beat/models.py:265
msgid "Minute(s)"
msgstr "分钟"

#: django_celery_beat/models.py:267
msgid "Cron Minutes to Run. Use \"*\" for \"all\". (Example: \"0,30\")"
msgstr "计划执行的分钟。 将\"*\"用作\"all\"。（例如：\"0,30\"）"

#: django_celery_beat/models.py:272
msgid "Hour(s)"
msgstr "小时"

#: django_celery_beat/models.py:274
msgid "Cron Hours to Run. Use \"*\" for \"all\". (Example: \"8,20\")"
msgstr "计划执行的小时。 将\"*\"用作\"all\"。（例如：\"8,20\"）"

#: django_celery_beat/models.py:279
msgid "Day(s) Of The Month"
msgstr "一个月的第几天"

#: django_celery_beat/models.py:281
msgid ""
"Cron Days Of The Month to Run. Use \"*\" for \"all\". (Example: \"1,15\")"
msgstr "计划执行的每个月的第几天。将\"*\"用作\"all\"。（例如：\"0,5\"）"

#: django_celery_beat/models.py:287
msgid "Month(s) Of The Year"
msgstr "一年的第几个月"

#: django_celery_beat/models.py:289
#, fuzzy
#| msgid ""
#| "Cron Months Of The Year to Run. Use \"*\" for \"all\". (Example: \"0,6\")"
msgid ""
"Cron Months (1-12) Of The Year to Run. Use \"*\" for \"all\". (Example: "
"\"1,12\")"
msgstr "计划执行的每一年的第几个月。将\"*\"用作\"all\"。（例如：\"0,5\"）"

#: django_celery_beat/models.py:295
msgid "Day(s) Of The Week"
msgstr "一个星期的第几天"

#: django_celery_beat/models.py:297
#, fuzzy
#| msgid ""
#| "Cron Days Of The Week to Run. Use \"*\" for \"all\". (Example: \"0,5\")"
msgid ""
"Cron Days Of The Week to Run. Use \"*\" for \"all\", Sunday is 0 or 7, "
"Monday is 1. (Example: \"0,5\")"
msgstr "计划执行的每周的第几天。将\"*\"用作\"all\"。（例如：\"0,5\"）"

#: django_celery_beat/models.py:305
msgid "Cron Timezone"
msgstr "计划任务的时区"

#: django_celery_beat/models.py:307
msgid "Timezone to Run the Cron Schedule on. Default is UTC."
msgstr "执行计划任务表的时区。 默认为UTC。"

#: django_celery_beat/models.py:313
msgid "crontab"
msgstr "计划任务"

#: django_celery_beat/models.py:314
msgid "crontabs"
msgstr "计划任务"

#: django_celery_beat/models.py:392
msgid "periodic task track"
msgstr "周期性任务追踪"

#: django_celery_beat/models.py:393
msgid "periodic task tracks"
msgstr "周期性任务追踪"

#: django_celery_beat/models.py:417
msgid "Name"
msgstr "任务名"

#: django_celery_beat/models.py:418
msgid "Short Description For This Task"
msgstr "该任务的简短说明"

#: django_celery_beat/models.py:423
msgid ""
"The Name of the Celery Task that Should be Run.  (Example: \"proj.tasks."
"import_contacts\")"
msgstr "被执行的任务的名称。（例如：\"proj.tasks.import_contacts\")"

#: django_celery_beat/models.py:431
msgid "Interval Schedule"
msgstr "间隔时间表"

#: django_celery_beat/models.py:432
msgid ""
"Interval Schedule to run the task on.  Set only one schedule type, leave the "
"others null."
msgstr "执行任务的间隔时间表。 仅设置一种时间表类型，将其他保留为空。"

#: django_celery_beat/models.py:437
msgid "Crontab Schedule"
msgstr "计划时间表"

#: django_celery_beat/models.py:438
msgid ""
"Crontab Schedule to run the task on.  Set only one schedule type, leave the "
"others null."
msgstr "执行任务的计划时间表。 仅设置一种时间表类型，将其他保留为空。"

#: django_celery_beat/models.py:443
msgid "Solar Schedule"
msgstr "日程时间表"

#: django_celery_beat/models.py:444
msgid ""
"Solar Schedule to run the task on.  Set only one schedule type, leave the "
"others null."
msgstr "执行任务的日程时间表。 仅设置一种时间表类型，将其他保留为空。"

#: django_celery_beat/models.py:449
msgid "Clocked Schedule"
msgstr "定时时间表"

#: django_celery_beat/models.py:450
msgid ""
"Clocked Schedule to run the task on.  Set only one schedule type, leave the "
"others null."
msgstr "执行任务的定时时间表。 仅设置一种时间表类型，将其他保留为空。"

#: django_celery_beat/models.py:456
msgid "Positional Arguments"
msgstr "位置参数"

#: django_celery_beat/models.py:458
msgid "JSON encoded positional arguments (Example: [\"arg1\", \"arg2\"])"
msgstr "JSON编码的位置参数(例如: [\"arg1\", \"arg2\"])"

#: django_celery_beat/models.py:463
msgid "Keyword Arguments"
msgstr "关键字参数"

#: django_celery_beat/models.py:465
msgid "JSON encoded keyword arguments (Example: {\"argument\": \"value\"})"
msgstr "JSON编码的关键字参数(例如: {\"argument\": \"value\"})"

#: django_celery_beat/models.py:471
msgid "Queue Override"
msgstr "队列覆盖"

#: django_celery_beat/models.py:473
msgid "Queue defined in CELERY_TASK_QUEUES. Leave None for default queuing."
msgstr "在 CELERY_TASK_QUEUES 定义的队列。保留空以进行默认排队。"

#: django_celery_beat/models.py:482
msgid "Exchange"
msgstr "交换机"

#: django_celery_beat/models.py:483
msgid "Override Exchange for low-level AMQP routing"
msgstr "覆盖交换机以进行低层级AMQP路由"

#: django_celery_beat/models.py:487
msgid "Routing Key"
msgstr "路由键"

#: django_celery_beat/models.py:488
msgid "Override Routing Key for low-level AMQP routing"
msgstr "覆盖路由键以进行低层级AMQP路由"

#: django_celery_beat/models.py:492
msgid "AMQP Message Headers"
msgstr "AMQP消息头"

#: django_celery_beat/models.py:493
msgid "JSON encoded message headers for the AMQP message."
msgstr "AMQP消息的JSON编码消息头。"

#: django_celery_beat/models.py:499
msgid "Priority"
msgstr "优先级"

#: django_celery_beat/models.py:501
msgid ""
"Priority Number between 0 and 255. Supported by: RabbitMQ, Redis (priority "
"reversed, 0 is highest)."
msgstr ""
"优先级数字，介于0和255之间。支持者：RabbitMQ，Redis（优先级颠倒，0是最高）。"

#: django_celery_beat/models.py:506
msgid "Expires Datetime"
msgstr "过期时刻"

#: django_celery_beat/models.py:508
msgid ""
"Datetime after which the schedule will no longer trigger the task to run"
msgstr "过期时刻，计划表将在此时刻后不再触发任务执行"

#: django_celery_beat/models.py:513
msgid "Expires timedelta with seconds"
msgstr "过期时间间隔，以秒为单位"

#: django_celery_beat/models.py:515
msgid ""
"Timedelta with seconds which the schedule will no longer trigger the task to "
"run"
msgstr "再过该秒后，不再触发任务执行"

#: django_celery_beat/models.py:521
msgid "One-off Task"
msgstr "一次任务"

#: django_celery_beat/models.py:523
msgid "If True, the schedule will only run the task a single time"
msgstr "如果为True，则计划将仅运行任务一次"

#: django_celery_beat/models.py:527
msgid "Start Datetime"
msgstr "开始时间"

#: django_celery_beat/models.py:529
msgid "Datetime when the schedule should begin triggering the task to run"
msgstr "时间表开始触发任务执行的时刻"

#: django_celery_beat/models.py:534
msgid "Enabled"
msgstr "已启用"

#: django_celery_beat/models.py:535
msgid "Set to False to disable the schedule"
msgstr "设置为False可禁用时间表"

#: django_celery_beat/models.py:540
msgid "Last Run Datetime"
msgstr "上次运行时刻"

#: django_celery_beat/models.py:542
msgid ""
"Datetime that the schedule last triggered the task to run. Reset to None if "
"enabled is set to False."
msgstr "最后一次触发任务执行的时刻。 如果enabled设置为False，则重置为None。"

#: django_celery_beat/models.py:547
msgid "Total Run Count"
msgstr "总运行次数"

#: django_celery_beat/models.py:549
msgid "Running count of how many times the schedule has triggered the task"
msgstr "任务执行多少次的运行计数"

#: django_celery_beat/models.py:554
msgid "Last Modified"
msgstr "最后修改"

#: django_celery_beat/models.py:555
msgid "Datetime that this PeriodicTask was last modified"
msgstr "该周期性任务的最后修改时刻"

#: django_celery_beat/models.py:559
msgid "Description"
msgstr "描述"

#: django_celery_beat/models.py:561
msgid "Detailed description about the details of this Periodic Task"
msgstr "有关此周期性任务的详细信息"

#: django_celery_beat/models.py:570
msgid "periodic task"
msgstr "周期性任务"

#: django_celery_beat/models.py:571
msgid "periodic tasks"
msgstr "周期性任务"

#: django_celery_beat/templates/admin/djcelery/change_list.html:6
msgid "Home"
msgstr "首页"
