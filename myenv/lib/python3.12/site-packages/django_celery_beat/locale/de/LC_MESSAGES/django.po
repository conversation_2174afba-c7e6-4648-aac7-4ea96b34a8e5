# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-22 18:56+0000\n"
"PO-Revision-Date: 2022-08-28 03:29+0200\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.1.1\n"

#: django_celery_beat/admin.py:60
msgid "Task (registered)"
msgstr "Aufgabe (registriert)"

#: django_celery_beat/admin.py:64
msgid "Task (custom)"
msgstr "Aufgabe (benutzerdefiniert)"

#: django_celery_beat/admin.py:81
msgid "Need name of task"
msgstr "Name der Aufgabe benötigt"

#: django_celery_beat/admin.py:87 django_celery_beat/models.py:605
msgid "Only one can be set, in expires and expire_seconds"
msgstr ""
"Es kann nur eine festgelegt werden, \"Ablaufdatum\" oder \"Ablauf-Timedelta "
"in Sekunden\""

#: django_celery_beat/admin.py:97
#, python-format
msgid "Unable to parse JSON: %s"
msgstr "Ausserstande JSON zu parsen: %s"

#: django_celery_beat/admin.py:125
msgid "Schedule"
msgstr "Zeitplan"

#: django_celery_beat/admin.py:130
msgid "Arguments"
msgstr "Argumente"

#: django_celery_beat/admin.py:134
msgid "Execution Options"
msgstr "Ausführungsoptionen"

#: django_celery_beat/admin.py:177
#, python-brace-format
msgid "{0} task{1} {2} successfully {3}"
msgstr "{0} Aufgabe{1} {2} erfolgreich {3}"

#: django_celery_beat/admin.py:180 django_celery_beat/admin.py:247
msgid "was,were"
msgstr "war,waren"

#: django_celery_beat/admin.py:189
msgid "Enable selected tasks"
msgstr "Ausgewählte Aufgaben aktivieren"

#: django_celery_beat/admin.py:195
msgid "Disable selected tasks"
msgstr "Ausgewählte Aufgaben deaktivieren"

#: django_celery_beat/admin.py:207
msgid "Toggle activity of selected tasks"
msgstr "Aktivität ausgewählter Aufgaben ein-/ausschalten"

#: django_celery_beat/admin.py:228
#, fuzzy, python-brace-format
#| msgid "task \"{0}\" not found"
msgid "task \"{not_found_task_name}\" not found"
msgstr "Aufgabe \"{0}\" nicht gefunden"

#: django_celery_beat/admin.py:244
#, python-brace-format
msgid "{0} task{1} {2} successfully run"
msgstr "{0} Aufgabe{1} {2} erfolgreich ausgeführt"

#: django_celery_beat/admin.py:250
msgid "Run selected tasks"
msgstr "Ausgewählte Aufgaben ausführen"

#: django_celery_beat/apps.py:13
msgid "Periodic Tasks"
msgstr "Periodische Aufgaben"

#: django_celery_beat/models.py:30
msgid "Days"
msgstr "Tage"

#: django_celery_beat/models.py:31
msgid "Hours"
msgstr "Stunden"

#: django_celery_beat/models.py:32
msgid "Minutes"
msgstr "Minuten"

#: django_celery_beat/models.py:33
msgid "Seconds"
msgstr "Sekunden"

#: django_celery_beat/models.py:34
msgid "Microseconds"
msgstr "Mikrosekunden"

#: django_celery_beat/models.py:38
msgid "Day"
msgstr "Tag"

#: django_celery_beat/models.py:39
msgid "Hour"
msgstr "Stunde"

#: django_celery_beat/models.py:40
msgid "Minute"
msgstr "Minute"

#: django_celery_beat/models.py:41
msgid "Second"
msgstr "Sekunde"

#: django_celery_beat/models.py:42
msgid "Microsecond"
msgstr "Mikrosekunde"

#: django_celery_beat/models.py:46
msgid "Astronomical dawn"
msgstr "Astronomische Morgendämmerung"

#: django_celery_beat/models.py:47
msgid "Civil dawn"
msgstr "Zivile Morgendämmerung"

#: django_celery_beat/models.py:48
msgid "Nautical dawn"
msgstr "Nautische Morgendämmerung"

#: django_celery_beat/models.py:49
msgid "Astronomical dusk"
msgstr "Astronomische Dämmerung"

#: django_celery_beat/models.py:50
msgid "Civil dusk"
msgstr "Zivile Dämmerung"

#: django_celery_beat/models.py:51
msgid "Nautical dusk"
msgstr "Nautische Dämmerung"

#: django_celery_beat/models.py:52
msgid "Solar noon"
msgstr "Sonnenmittag"

#: django_celery_beat/models.py:53
msgid "Sunrise"
msgstr "Sonnenaufgang"

#: django_celery_beat/models.py:54
msgid "Sunset"
msgstr "Sonnenuntergang"

#: django_celery_beat/models.py:88
msgid "Solar Event"
msgstr "Sonnenereigniss"

#: django_celery_beat/models.py:89
msgid "The type of solar event when the job should run"
msgstr "Die Art des Solarereignisses, wenn die Aufgabe ausgeführt werden soll"

#: django_celery_beat/models.py:93
msgid "Latitude"
msgstr "Breitengrad"

#: django_celery_beat/models.py:94
msgid "Run the task when the event happens at this latitude"
msgstr ""
"Führen Sie diese Aufgabe aus, wenn das Ereignis in diesem Breitengrad "
"auftritt"

#: django_celery_beat/models.py:99
msgid "Longitude"
msgstr "Längengrad"

#: django_celery_beat/models.py:100
msgid "Run the task when the event happens at this longitude"
msgstr ""
"Führen Sie diese Aufgabe aus, wenn das Ereignis in diesem Längengrad auftritt"

#: django_celery_beat/models.py:107
msgid "solar event"
msgstr "Sonnenereigniss"

#: django_celery_beat/models.py:108
msgid "solar events"
msgstr "Sonnenereignisse"

#: django_celery_beat/models.py:158
msgid "Number of Periods"
msgstr "Anzahl der Perioden"

#: django_celery_beat/models.py:159
msgid "Number of interval periods to wait before running the task again"
msgstr ""
"Anzahl der Intervallperioden, die gewartet werden sollen, bevor der Task "
"erneut ausgeführt wird"

#: django_celery_beat/models.py:165
msgid "Interval Period"
msgstr "Intervallperiode"

#: django_celery_beat/models.py:166
msgid "The type of period between task runs (Example: days)"
msgstr "Der Typ des Zeitraums zwischen den Taskläufen (Beispiel: Tage)"

#: django_celery_beat/models.py:172
msgid "interval"
msgstr "Intervall"

#: django_celery_beat/models.py:173
msgid "intervals"
msgstr "Intervalle"

#: django_celery_beat/models.py:200
msgid "every {}"
msgstr "jede {}"

#: django_celery_beat/models.py:205
msgid "every {} {}"
msgstr "jede {} {}"

#: django_celery_beat/models.py:216
msgid "Clock Time"
msgstr "Uhrzeit"

#: django_celery_beat/models.py:217
msgid "Run the task at clocked time"
msgstr "Die Aufgabe zur Uhrzeit ausführen"

#: django_celery_beat/models.py:223 django_celery_beat/models.py:224
msgid "clocked"
msgstr "Getaktet"

#: django_celery_beat/models.py:264
msgid "Minute(s)"
msgstr "Minute(n)"

#: django_celery_beat/models.py:266
msgid "Cron Minutes to Run. Use \"*\" for \"all\". (Example: \"0,30\")"
msgstr ""
"Cron Minuten zur Ausführung. Verwenden Sie \"*\" für \"alle\". (Beispiel: "
"\"0,30\")"

#: django_celery_beat/models.py:271
msgid "Hour(s)"
msgstr "Stunde(n)"

#: django_celery_beat/models.py:273
msgid "Cron Hours to Run. Use \"*\" for \"all\". (Example: \"8,20\")"
msgstr ""
"Cron Stunden zur Ausführung. Verwenden Sie \"*\" für \"alle\". (Beispiel: "
"„8,20“)"

#: django_celery_beat/models.py:278
msgid "Day(s) Of The Week"
msgstr "Tag(e) der Woche"

#: django_celery_beat/models.py:280
#, fuzzy
#| msgid ""
#| "Cron Days Of The Week to Run. Use \"*\" for \"all\". (Example: \"0,5\")"
msgid ""
"Cron Days Of The Week to Run. Use \"*\" for \"all\", Sunday is 0 or 7, "
"Monday is 1. (Example: \"0,5\")"
msgstr ""
"Cron Tage der Woche zur Ausführung. Verwenden Sie \"*\" für \"alle\", Sonntag ist 0 oder 7, "
"Montag ist 1. (Beispiel: \"0,5\")"

#: django_celery_beat/models.py:286
msgid "Day(s) Of The Month"
msgstr "Tag(e) des Monats"

#: django_celery_beat/models.py:288
msgid ""
"Cron Days Of The Month to Run. Use \"*\" for \"all\". (Example: \"1,15\")"
msgstr ""
"Cron Tage des Monats zur Ausführung. Verwenden Sie \"*\" für \"alle\". "
"(Beispiel: \"1,15\")"

#: django_celery_beat/models.py:294
msgid "Month(s) Of The Year"
msgstr "Monat(e) des Jahres"

#: django_celery_beat/models.py:296
#, fuzzy
#| msgid ""
#| "Cron Months Of The Year to Run. Use \"*\" for \"all\". (Example: \"0,6\")"
msgid ""
"Cron Months (1-12) Of The Year to Run. Use \"*\" for \"all\". (Example: "
"\"1,12\")"
msgstr ""
"Cron Monate des Jahres zur Ausführung. Verwenden Sie \"*\" für \"alle\". "
"(Beispiel: \"0,6\")"

#: django_celery_beat/models.py:304
msgid "Cron Timezone"
msgstr "Cron Zeitzone"

#: django_celery_beat/models.py:306
msgid "Timezone to Run the Cron Schedule on. Default is UTC."
msgstr ""
"Zeitzone, in der der Cron-Zeitplan ausgeführt werden soll. Der Standardwert "
"ist UTC."

#: django_celery_beat/models.py:312
msgid "crontab"
msgstr "Crontab"

#: django_celery_beat/models.py:313
msgid "crontabs"
msgstr "Crontabs"

#: django_celery_beat/models.py:404
msgid "Name"
msgstr "Name"

#: django_celery_beat/models.py:405
msgid "Short Description For This Task"
msgstr "Kurzbeschreibung für diese Aufgabe"

#: django_celery_beat/models.py:410
msgid ""
"The Name of the Celery Task that Should be Run.  (Example: \"proj.tasks."
"import_contacts\")"
msgstr ""
"Der Name des Celery-Tasks, der ausgeführt werden soll. (Beispiel: \"proj."
"tasks.import_contacts\")"

#: django_celery_beat/models.py:418
msgid "Interval Schedule"
msgstr "Intervall-Zeitplan"

#: django_celery_beat/models.py:419
msgid ""
"Interval Schedule to run the task on.  Set only one schedule type, leave the "
"others null."
msgstr ""
"Intervallzeitplan zum Ausführen der Aufgabe. Legen Sie nur einen Zeitplantyp "
"fest und lassen Sie die anderen null."

#: django_celery_beat/models.py:424
msgid "Crontab Schedule"
msgstr "Crontab-Zeitplan"

#: django_celery_beat/models.py:425
msgid ""
"Crontab Schedule to run the task on.  Set only one schedule type, leave the "
"others null."
msgstr ""
"Crontab Zeitplan zum Ausführen der Aufgabe. Legen Sie nur einen Zeitplantyp "
"fest und lassen Sie die anderen null."

#: django_celery_beat/models.py:430
msgid "Solar Schedule"
msgstr "Solar-Zeitplan"

#: django_celery_beat/models.py:431
msgid ""
"Solar Schedule to run the task on.  Set only one schedule type, leave the "
"others null."
msgstr ""
"Solarzeitplan, um die Aufgabe auszuführen. Legen Sie nur einen Zeitplantyp "
"fest und lassen Sie die anderen auf null."

#: django_celery_beat/models.py:436
msgid "Clocked Schedule"
msgstr "Getakteter-Zeitplan"

#: django_celery_beat/models.py:437
msgid ""
"Clocked Schedule to run the task on.  Set only one schedule type, leave the "
"others null."
msgstr ""
"Getakteter Zeitplan zum Ausführen der Aufgabe. Legen Sie nur einen "
"Zeitplantyp fest und lassen Sie die anderen null."

#: django_celery_beat/models.py:443
msgid "Positional Arguments"
msgstr "Positionsargumente"

#: django_celery_beat/models.py:445
msgid "JSON encoded positional arguments (Example: [\"arg1\", \"arg2\"])"
msgstr "JSON-Codierte Positionsargumente (Beispiel: [\"arg1\", \"arg2\"])"

#: django_celery_beat/models.py:450
msgid "Keyword Arguments"
msgstr "Schlüsselwort-Argumente"

#: django_celery_beat/models.py:452
msgid "JSON encoded keyword arguments (Example: {\"argument\": \"value\"})"
msgstr ""
"JSON-Codierte Schlüsselwortargumente (Beispiel: {\"argument\": \"wert\"})"

#: django_celery_beat/models.py:458
msgid "Queue Override"
msgstr "Warteschlangenüberschreibung"

#: django_celery_beat/models.py:460
msgid "Queue defined in CELERY_TASK_QUEUES. Leave None for default queuing."
msgstr ""
"In CELERY_TASK_QUEUES definierte Warteschlange. Auf None lassen, für die "
"Standardwarteschlange."

#: django_celery_beat/models.py:469
msgid "Exchange"
msgstr "Exchange"

#: django_celery_beat/models.py:470
msgid "Override Exchange for low-level AMQP routing"
msgstr "Override Exchange für Low-Level-AMQP-Routing"

#: django_celery_beat/models.py:474
msgid "Routing Key"
msgstr "Routing-Schlüssel"

#: django_celery_beat/models.py:475
msgid "Override Routing Key for low-level AMQP routing"
msgstr "Override Routing Key für Low-Level-AMQP-Routing"

#: django_celery_beat/models.py:479
msgid "AMQP Message Headers"
msgstr "AMQP-Nachrichtenheader"

#: django_celery_beat/models.py:480
msgid "JSON encoded message headers for the AMQP message."
msgstr "JSON-Codierte Nachrichtenheader für die AMQP-Nachricht."

#: django_celery_beat/models.py:486
msgid "Priority"
msgstr "Priorität"

#: django_celery_beat/models.py:488
msgid ""
"Priority Number between 0 and 255. Supported by: RabbitMQ, Redis (priority "
"reversed, 0 is highest)."
msgstr ""
"Prioritätsnummer zwischen 0 und 255. Unterstützt von: RabbitMQ, Redis "
"(Priorität umgekehrt, 0 ist am höchsten)."

#: django_celery_beat/models.py:493
msgid "Expires Datetime"
msgstr "Ablaufdatum"

#: django_celery_beat/models.py:495
msgid ""
"Datetime after which the schedule will no longer trigger the task to run"
msgstr ""
"Zeitpunkt, nach dem der Zeitplan die Ausführung die Aufgabe nicht mehr "
"auslöst"

#: django_celery_beat/models.py:500
msgid "Expires timedelta with seconds"
msgstr "Ablauf-Timedelta in Sekunden"

#: django_celery_beat/models.py:502
msgid ""
"Timedelta with seconds which the schedule will no longer trigger the task to "
"run"
msgstr ""
"Timedelta in Sekunden, die der Zeitplan nicht mehr auslöst, um die Aufgabe "
"auszuführen"

#: django_celery_beat/models.py:508
msgid "One-off Task"
msgstr "Einmalige Aufgabe"

#: django_celery_beat/models.py:510
msgid "If True, the schedule will only run the task a single time"
msgstr "Wenn aktiv, wird die Aufgabe im Zeitplan nur einmal ausgeführt"

#: django_celery_beat/models.py:514
msgid "Start Datetime"
msgstr "Start-Datum"

#: django_celery_beat/models.py:516
msgid "Datetime when the schedule should begin triggering the task to run"
msgstr ""
"Zeitpunkt, zu der der Zeitplan beginnen soll, die Ausführung der Aufgabe "
"auszulösen"

#: django_celery_beat/models.py:521
msgid "Enabled"
msgstr "Aktiviert"

#: django_celery_beat/models.py:522
msgid "Set to False to disable the schedule"
msgstr "Deaktivieren zum deaktivieren -_-"

#: django_celery_beat/models.py:527
msgid "Last Run Datetime"
msgstr "Uhrzeit der letzten Ausführung"

#: django_celery_beat/models.py:529
msgid ""
"Datetime that the schedule last triggered the task to run. Reset to None if "
"enabled is set to False."
msgstr ""
"Uhrzeit, zu der der letzte Zeitplan die Ausführung die Aufgabe ausgelöst "
"hat. Setzt auf Keine zurück, wenn \"Aktiviert\" ist auf False gesetzt wird."

#: django_celery_beat/models.py:534
msgid "Total Run Count"
msgstr "Gesamtzahl der Durchgänge"

#: django_celery_beat/models.py:536
msgid "Running count of how many times the schedule has triggered the task"
msgstr "Laufende Zählung, wie oft der Zeitplan die Aufgabe ausgelöst hat"

#: django_celery_beat/models.py:541
msgid "Last Modified"
msgstr "Zuletzt geändert"

#: django_celery_beat/models.py:542
msgid "Datetime that this PeriodicTask was last modified"
msgstr "Uhrzeit zu dem diese periodische Aufgabe zuletzt modifiziert wurde"

#: django_celery_beat/models.py:546
msgid "Description"
msgstr "Beschreibung"

#: django_celery_beat/models.py:548
msgid "Detailed description about the details of this Periodic Task"
msgstr "Detaillierte Beschreibung der Details dieser periodischen Aufgabe"

#: django_celery_beat/models.py:557
msgid "periodic task"
msgstr "Periodische Aufgabe"

#: django_celery_beat/models.py:558
msgid "periodic tasks"
msgstr "Periodische Aufgaben"

#: django_celery_beat/templates/admin/djcelery/change_list.html:6
msgid "Home"
msgstr "Start"
