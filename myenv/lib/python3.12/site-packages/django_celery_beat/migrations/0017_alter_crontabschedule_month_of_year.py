# Generated by Django 4.1.4 on 2022-12-17 09:21

from django.db import migrations, models
import django_celery_beat.validators


class Migration(migrations.Migration):

    dependencies = [
        ('django_celery_beat', '0016_alter_crontabschedule_timezone'),
    ]

    operations = [
        migrations.AlterField(
            model_name='crontabschedule',
            name='month_of_year',
            field=models.CharField(default='*', help_text='Cron Months (1-12) Of The Year to Run. Use "*" for "all". (Example: "1,12")', max_length=64, validators=[django_celery_beat.validators.month_of_year_validator], verbose_name='Month(s) Of The Year'),
        ),
    ]
