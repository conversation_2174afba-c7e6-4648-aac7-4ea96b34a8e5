# Generated by Django 3.2.16 on 2022-12-23 22:30

from django.db import migrations, models
import django_celery_beat.validators


class Migration(migrations.Migration):

    dependencies = [
        ('django_celery_beat', '0017_alter_crontabschedule_month_of_year'),
    ]

    operations = [
        migrations.AlterField(
            model_name='crontabschedule',
            name='day_of_week',
            field=models.CharField(default='*', help_text='Cron Days Of The Week to Run. Use "*" for "all", Sunday is 0 or 7, Monday is 1. (Example: "0,5")', max_length=64, validators=[django_celery_beat.validators.day_of_week_validator], verbose_name='Day(s) Of The Week'),
        ),
    ]
