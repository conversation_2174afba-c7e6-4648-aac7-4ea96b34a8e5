services:
  nginx:
    build: ./nginx
    image: nginx
    container_name: "nginx_catalog"
    ports:
      - 80:80
    depends_on:
      - django_catalog
  django_catalog:
    image: django_app
    build:
      context: ./django
    container_name: "django_catalog"
    volumes:
      - ./django:/django
      - ./django/static:/django/static
    ports:
      - 8000:8000
    command: sh -c "python manage.py makemigrations &&
           
           gunicorn core.wsgi --workers 3 --timeout 120 --bind 0.0.0.0:8000"
    env_file:
      - "./django/.env"
    restart: on-failure
    depends_on:
      - db
      - redis
  celery:
    build:
      context: ./django
    image: django_app
    container_name: "celery_worker"
    command: celery -A core worker -l info --concurrency=4
    volumes:
      - ./django:/django
    env_file:
      - "./django/.env"
    depends_on:
      - db
      - redis
  beat:
    build:
      context: ./django
    image: django_app
    container_name: "celery_beat"
    command: celery -A core beat -l info
    volumes:
      - ./django:/django
    env_file:
      - "./django/.env"
    depends_on:
      - db
      - redis
  redis:
    image: redis:7
    container_name: "redis_catalog"
    ports:
      - 6379:6379
  db:
    image: mysql
    container_name: "db_catalog"
    ports:
      - 3307:3306
    environment:
      - MYSQL_ROOT_PASSWORD=catalog_password
      - MYSQL_USER=catalog_user
      - MYSQL_PASSWORD=catalog_password
      - MYSQL_DATABASE=catalog_db
    volumes:
      - ./data/mysql/db:/var/lib/mysql
    healthcheck:
      test: [ "CMD", "mysqladmin", "ping", "-h", "localhost" ]
      interval: 10s
      timeout: 5s
      retries: 5