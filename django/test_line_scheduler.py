#!/usr/bin/env python
"""
Test script for LINE message scheduling system
"""
import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.utils import timezone
from myapp.models import LineMessageSetting, CustomerCode
from myapp.tasks import check_and_send_scheduled_messages, send_line_message_task


def create_test_message():
    """Create a test LINE message for scheduling"""
    print("Creating test LINE message...")
    
    # Create a message scheduled for 2 minutes from now
    delivery_time = timezone.now() + timedelta(minutes=2)
    
    message = LineMessageSetting.objects.create(
        delivery_datetime=delivery_time,
        management_title="Test Scheduled Message",
        format_type="flex",
        product_code="TEST001",
        product_title="Test Product",
        product_price="¥1,000",
        product_description="This is a test product for scheduled messaging",
        product_url="https://example.com/product/test001",
        login_account_id="test_user",
        image_url="https://via.placeholder.com/300x200",
        status="配信予定"
    )
    
    # Add test customer codes
    CustomerCode.objects.create(line_message=message, code="CUSTOMER001")
    CustomerCode.objects.create(line_message=message, code="CUSTOMER002")
    
    print(f"Created message ID: {message.id}")
    print(f"Scheduled for: {delivery_time}")
    print(f"Status: {message.status}")
    print(f"Customer codes: {message.customer_codes.count()}")
    
    return message


def test_immediate_send(message_id):
    """Test immediate sending of a message"""
    print(f"\nTesting immediate send for message ID: {message_id}")
    
    try:
        message = LineMessageSetting.objects.get(id=message_id)
        print(f"Message status before: {message.status}")
        
        # Send the message immediately
        result = send_line_message_task.delay(message_id)
        print(f"Task queued with ID: {result.id}")
        
        # Wait a bit and check result
        import time
        time.sleep(2)
        
        # Refresh message from database
        message.refresh_from_db()
        print(f"Message status after: {message.status}")
        
    except LineMessageSetting.DoesNotExist:
        print(f"Message with ID {message_id} not found")


def test_scheduled_check():
    """Test the scheduled message check function"""
    print("\nTesting scheduled message check...")
    
    # Get due messages
    due_messages = LineMessageSetting.get_due_messages()
    print(f"Found {due_messages.count()} due messages")
    
    for message in due_messages:
        print(f"- ID: {message.id}, Title: {message.management_title}, Due: {message.delivery_datetime}")
    
    if due_messages.exists():
        # Run the check task
        result = check_and_send_scheduled_messages.delay()
        print(f"Scheduled check task queued with ID: {result.id}")
    else:
        print("No due messages to process")


def show_message_status():
    """Show status of all messages"""
    print("\n--- All LINE Messages Status ---")
    
    messages = LineMessageSetting.objects.filter(deleted_at__isnull=True).order_by('-created_at')
    
    if not messages.exists():
        print("No messages found")
        return
    
    for message in messages:
        print(f"\nID: {message.id}")
        print(f"Title: {message.management_title}")
        print(f"Status: {message.status}")
        print(f"Scheduled: {message.delivery_datetime}")
        print(f"Sent at: {message.sent_at}")
        print(f"Is due: {message.is_due}")
        print(f"Can be sent: {message.can_be_sent}")
        print(f"Customer codes: {message.customer_codes.count()}")
        if message.error_message:
            print(f"Error: {message.error_message}")


def main():
    print("=== LINE Message Scheduling Test ===")
    
    while True:
        print("\nOptions:")
        print("1. Create test message")
        print("2. Test immediate send")
        print("3. Test scheduled check")
        print("4. Show message status")
        print("5. Exit")
        
        choice = input("\nEnter your choice (1-5): ").strip()
        
        if choice == '1':
            create_test_message()
        elif choice == '2':
            message_id = input("Enter message ID: ").strip()
            try:
                test_immediate_send(int(message_id))
            except ValueError:
                print("Invalid message ID")
        elif choice == '3':
            test_scheduled_check()
        elif choice == '4':
            show_message_status()
        elif choice == '5':
            print("Exiting...")
            break
        else:
            print("Invalid choice")


if __name__ == '__main__':
    main()
