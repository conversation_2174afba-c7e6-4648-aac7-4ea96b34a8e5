# Generated by Django 5.2.1 on 2025-09-09 08:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('myapp', '0011_linemessagesetting_image_url'),
    ]

    operations = [
        migrations.AddField(
            model_name='linemessagesetting',
            name='error_message',
            field=models.TextField(blank=True, null=True, verbose_name='エラーメッセージ'),
        ),
        migrations.AddField(
            model_name='linemessagesetting',
            name='retry_count',
            field=models.IntegerField(default=0, verbose_name='リトライ回数'),
        ),
        migrations.AddField(
            model_name='linemessagesetting',
            name='sent_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='配信実行日時'),
        ),
        migrations.AlterField(
            model_name='linemessagesetting',
            name='status',
            field=models.CharField(choices=[('配信予定', '配信予定'), ('配信中', '配信中'), ('配信完了', '配信完了'), ('一部配信完了', '一部配信完了'), ('エラー', 'エラー'), ('キャンセル', 'キャンセル')], default='配信予定', max_length=50, verbose_name='ステータス'),
        ),
        migrations.AddIndex(
            model_name='linemessagesetting',
            index=models.Index(fields=['delivery_datetime', 'status'], name='line_messag_deliver_b43a1d_idx'),
        ),
        migrations.AddIndex(
            model_name='linemessagesetting',
            index=models.Index(fields=['status'], name='line_messag_status_b23ed8_idx'),
        ),
    ]
