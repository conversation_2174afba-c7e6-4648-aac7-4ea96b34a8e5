import pytz
from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.db.models import Q
from .models import ProductItem, ProductItemMasters, normalize_japanese, LineMessageSetting, CustomerCode
from .forms import UploadCSVForm
import tempfile
from django.contrib import messages
import os
from rest_framework.views import APIView
from .serializers import SyncProductCodeSerializer, ProductSerializer, UploadImageSerializer
from rest_framework import status
from rest_framework.response import Response
from django.utils import timezone
from django.shortcuts import redirect
from django.contrib.auth import login
import base64
import json
import time
from django.http import HttpResponse, HttpResponseRedirect
from Crypto.Cipher import AES
from .sso_user import sso_login_required
from .admin import load_list_image_product
from django.forms.models import model_to_dict
from .sysn_image import call_url_syns_image
from rest_framework.decorators import api_view
from django.conf import settings
from django.utils.timezone import now, localtime
import requests
from django.http import JsonResponse
import datetime

@sso_login_required
def import_product_items(request):
    if request.method == 'POST':
        form = UploadCSVForm(request.POST, request.FILES)
        if form.is_valid():
            # Save the uploaded file to a temporary location
            csv_file = request.FILES['csv_file']
            with tempfile.NamedTemporaryFile(delete=False, suffix='.csv') as temp_file:
                temp_file_path = temp_file.name
                for chunk in csv_file.chunks():
                    temp_file.write(chunk)

            # Process the CSV file
            try:
                success_count, error_count, error_messages = ProductItem.import_from_csv(temp_file_path)

                # Display results
                messages.success(
                    request,
                    f'Successfully imported {success_count} product items'
                )

                if error_count > 0:
                    messages.warning(
                        request,
                        f'Failed to import {error_count} product items'
                    )

                    for error in error_messages[:10]:  # Show first 10 errors
                        messages.error(request, error)

                    if len(error_messages) > 10:
                        messages.error(
                            request,
                            f'... and {len(error_messages) - 10} more errors'
                        )

            finally:
                # Clean up the temporary file
                os.unlink(temp_file_path)

            return redirect('import_product_items')
    else:
        form = UploadCSVForm()

    return render(request, 'import_product_items.html', {'form': form})


@sso_login_required
def import_product_item_masters(request):
    if request.method == 'POST':
        form = UploadCSVForm(request.POST, request.FILES)
        if form.is_valid():
            # Save the uploaded file to a temporary location
            csv_file = request.FILES['csv_file']
            with tempfile.NamedTemporaryFile(delete=False, suffix='.csv') as temp_file:
                temp_file_path = temp_file.name
                for chunk in csv_file.chunks():
                    temp_file.write(chunk)

            # Process the CSV file
            try:
                success_count, error_count, error_messages = ProductItemMasters.import_from_csv(temp_file_path)

                # Display results
                messages.success(
                    request,
                    f'Successfully imported {success_count} product item masters'
                )

                if error_count > 0:
                    messages.warning(
                        request,
                        f'Failed to import {error_count} product item masters'
                    )

                    for error in error_messages[:10]:  # Show first 10 errors
                        messages.error(request, error)

                    if len(error_messages) > 10:
                        messages.error(
                            request,
                            f'... and {len(error_messages) - 10} more errors'
                        )

            finally:
                # Clean up the temporary file
                os.unlink(temp_file_path)

            return redirect('import_product_item_masters')
    else:
        form = UploadCSVForm()

    return render(request, 'import_product_item_masters.html', {'form': form})


@sso_login_required
def product_list(request):
    # Get query parameters
    search_query = request.GET.get('search', '')
    is_check_sync = request.GET.get('is_check_sync', '')
    sort_by = request.GET.get('sort', 'product_code')
    page_size = int(request.GET.get('page_size', 20))  # Default 20 items per page

    products = ProductItem.objects
    # Apply search filter if provided
    if search_query:
        products = products.filter(
            Q(product_code__icontains=search_query) |
            Q(product_name_search__icontains=search_query) |
            Q(product_kana_search__icontains=search_query)
        )

    # Apply maker filter if provided
    if is_check_sync:
        products = products.filter(is_check_sync=is_check_sync)

    # Get list of all makers for the filter dropdown

    # Apply sorting
    if sort_by.startswith('-'):
        products = products.order_by(sort_by)
    else:
        products = products.order_by(sort_by)
    # Paginate the results
    page = request.GET.get('page', 1)
    paginator = Paginator(products, page_size)

    try:
        products_page = paginator.page(page)
    except PageNotAnInteger:
        products_page = paginator.page(1)
    except EmptyPage:
        products_page = paginator.page(paginator.num_pages)
    # Calculate page range for pagination display
    page_range = get_page_range(paginator, products_page.number)
    sizes = [20, 50, 100]
    tokyo_tz = pytz.timezone("Asia/Tokyo")
    for pro in products_page:
        if pro.check_sync_at is not None:
            pro.check_sync_time_jp = pro.check_sync_at.astimezone(tokyo_tz).strftime("%Y/%m/%d %H:%M")
        pro.created_at_jp = pro.created_at.astimezone(tokyo_tz).strftime("%Y/%m/%d %H:%M")
    context = {
        'loginName': request.session.get('sso_user')['companyCode'],
        'products': products_page,
        'search_query': search_query,
        'is_check_sync': is_check_sync,
        'sort_by': sort_by,
        'page_obj': products_page,
        'page_range': page_range,
        'page_size': page_size,
        'total_count': paginator.count,
        'sizes': sizes,
        'timestamp': int(now().timestamp())
    }

    return render(request, 'product_list.html', context)


def get_page_range(paginator, current_page, window=5):
    """
    Returns a range of page numbers to display in pagination
    with the current page in the center of a window of pages
    """
    total_pages = paginator.num_pages

    # If total pages is less than window, show all pages
    if total_pages <= window:
        return range(1, total_pages + 1)

    # Calculate the window of pages to show
    half_window = window // 2

    # If current page is close to the beginning
    if current_page <= half_window:
        return range(1, window + 1)

    # If current page is close to the end
    if current_page >= total_pages - half_window:
        return range(total_pages - window + 1, total_pages + 1)

    # Current page is in the middle
    return range(current_page - half_window, current_page + half_window + 1)


@sso_login_required
def product_detail(request, product_id):
    """
    View to display product details
    """
    product = get_object_or_404(ProductItem, id=product_id)
    if product.sync_product_code:
        productItemMaster = get_object_or_404(ProductItemMasters, product_code=product.sync_product_code)
    else:
        productItemMaster = None
    # Get query parameters
    search_query = request.GET.get('search', '')
    search_query_search = normalize_japanese(request.GET.get('search', ''))
    maker_name = request.GET.get('maker_name', '')
    sort_by = request.GET.get('sort', 'product_code')
    page_size = int(request.GET.get('page_size', 20))  # Default 20 items per page
    products = ProductItemMasters.objects
    # Apply search filter if provided
    if search_query:
        products = products.filter(
            Q(individual_item_jan_code=search_query_search) |
            Q(product_name_search__icontains=search_query_search) |
            Q(product_kana_search__icontains=search_query_search)
        )

    # Apply maker filter if provided
    if maker_name:
        products = products.filter(manufacturer_name=maker_name)

    # Get list of all makers for the filter dropdown

    # Apply sorting
    if sort_by.startswith('-'):
        products = products.order_by(sort_by)
    else:
        products = products.order_by(sort_by)
    # Paginate the results
    page = request.GET.get('page', 1)
    paginator = Paginator(products, page_size)

    try:
        products_page = paginator.page(page)
    except PageNotAnInteger:
        products_page = paginator.page(1)
    except EmptyPage:
        products_page = paginator.page(paginator.num_pages)
    page_range = get_page_range(paginator, products_page.number)
    maker_list = ProductItemMasters.objects.values_list('manufacturer_name', flat=True).distinct()
    sizes = [20, 50, 100]
    list_images = []
    # load list image by products_page
    if product.sync_product_code:
        countImage = settings.COUNT_IMAGE
        list_images = load_list_image_product(product.sync_product_code, countImage)

    for item in products_page:
        base_dict = model_to_dict(item)
        listImages = load_list_image_product(base_dict['product_code'], 2)
        item.image = listImages[0] if listImages else ""
        item.list_images = listImages
    context = {
        'loginName': request.session.get('sso_user')['companyCode'],
        'products': products_page,
        'search_query': search_query,
        'maker_name': maker_name,
        'sort_by': sort_by,
        'page_obj': products_page,
        'page_range': page_range,
        'page_size': page_size,
        'total_count': paginator.count,
        'product': product,
        'maker_list': maker_list,
        'sizes': sizes,
        'productItemMaster': productItemMaster,
        'list_images': list_images,
        'timestamp': int(now().timestamp())
    }

    return render(request, 'product_detail.html', context)


class UpdateSyncProductCodeAPIView(APIView):
    def patch(self, request, id):
        product = get_object_or_404(ProductItem, id=id)
        data = request.data.copy()
        data['is_check_sync'] = '1'
        data['check_sync_at'] = timezone.now()
        serializer = SyncProductCodeSerializer(product, data=data, partial=True)
        if serializer.is_valid():
            serializer.save()
            call_url_syns_image(data['sync_product_code'], product.product_code)
            return Response({'message': 'Sync product code updated successfully'})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


def sso_login(request):
    SECRET_KEY = base64.urlsafe_b64decode('1XlhilHWUKI8-wnFJamS65PuFh21Koi21amf6lIRg4s=')
    token = request.GET.get('sso_token')
    if not token:
        return HttpResponse("❌ token is required", status=400)
    try:
        raw = base64.b64decode(token)
        iv = raw[:16]
        ciphertext = raw[16:]

        cipher = AES.new(SECRET_KEY, AES.MODE_CBC, iv)
        decrypted = cipher.decrypt(ciphertext)

        # remove padding (PKCS7)
        pad_len = decrypted[-1]
        decrypted = decrypted[:-pad_len]

        user_data = json.loads(decrypted)
    except Exception as e:
        return HttpResponse(f"❌ token invalid: {str(e)}", status=400)

        # check expiration
    if user_data.get("exp", 0) < time.time():
        return HttpResponse("❌ Token expired", status=403)

    user_data = {
        "loginId": user_data.get("loginId"),
        "loginName": user_data.get("loginName"),
        "companyCode": user_data.get("companyCode"),
        "nextUrl": user_data.get("nextUrl"),
    }

    request.session['sso_user'] = {
        'loginId': user_data['loginId'],
        'loginName': user_data['loginName'],
        'companyCode': user_data['companyCode'],
    }
    request.session.set_expiry(15 * 60)
    return HttpResponseRedirect("/" + user_data['nextUrl'])


@api_view(['POST'])
def sysn_info_product(request):
    products = request.data
    errors = []

    for product_data in products:
        product_code = product_data.get('product_code')
        if not product_code:
            errors.append({'error': 'Missing product_code', 'data': product_data})
            continue
        product_data['product_kana'] = normalize_japanese(product_data['product_name'])
        product_data['category_code2'] = product_data['category_code1']
        product_data['category_code3'] = product_data['category_code1']
        product_data['category_code4'] = product_data['category_code1']
        product_data['category_code5'] = product_data['category_code1']
        product_data['product_name_search'] = normalize_japanese(product_data['product_name'])
        product_data['product_kana_search'] = normalize_japanese(product_data['product_name'])
        product_data['created_at'] = timezone.now().isoformat()
        product_data['updated_at'] = timezone.now().isoformat()
        try:
            product = ProductItem.objects.get(product_code=product_code)
            serializer = ProductSerializer(product, data=product_data)
        except ProductItem.DoesNotExist:
            # Create new if not exists
            serializer = ProductSerializer(data=product_data)

        if serializer.is_valid():
            serializer.save()
        else:
            errors.append({'product_code': product_code, 'errors': serializer.errors})

    if errors:
        return Response({'status': 'partial_success', 'errors': errors}, status=207)
    return Response({'status': 'success'}, status=200)

@sso_login_required
def line_dashboard(request):
    context = {
        'timestamp': int(now().timestamp()),
        'search_query' : request.GET.get('search_query', ''),
        'status' : request.GET.get('status', '')

    }
    return render(request, 'line_dashboard.html', context)


@sso_login_required
def create_message_line(request):
    products = ProductItem.objects.all()
    context = {
        'timestamp': int(now().timestamp()),
        'products': products
    }
    return render(request, 'create_message_line.html', context)


@api_view(['GET'])
def get_product_item_by_code(request, product_code):
    try:
        product = ProductItem.objects.get(product_code=product_code)
        serializer = ProductSerializer(product)

        # Get product data and add images
        product_data = serializer.data
        product_data['image'] = ''
        try:
            # Option 1: Call external API
            external_api_url = f"{settings.URL_SMART_ORDER}/catalog/api/product/{product_code}"
            response = requests.get(external_api_url, timeout=2)
            if response.status_code == 200:
                external_data = response.json()
                product_data[
                    'url'] = f"{settings.URL_SMART_ORDER}/catalog/products/detail/{external_data['product_id']}"
            else:
                product_data['url'] = None

        except requests.exceptions.RequestException as e:
            product_data['url'] = None

        if product_data['is_check_sync']:
            product_data['image'] = settings.URL_SMART_ORDER + '/001/img/001/' + product_code + '_1.jpg'

        return Response(product_data, status=status.HTTP_200_OK)
    except ProductItem.DoesNotExist:
        return Response(
            {'error': f'Product with code {product_code} not found'},
            status=status.HTTP_404_NOT_FOUND
        )


@sso_login_required
def store_message_line(request):
    if request.method == 'POST':
        try:
            # Get data from POST request
            customer_codes_str = request.POST.get('customer_codes', '').strip()
            delivery_datetime_str = request.POST.get('delivery_datetime', '').strip()
            format_type = request.POST.get('format_type', '').strip()
            product_code = request.POST.get('product_code', '').strip()
            management_title = request.POST.get('management_title', '').strip()
            product_title = request.POST.get('product_title', '').strip()
            product_price = request.POST.get('product_price', '').strip()
            product_description = request.POST.get('product_description', '').strip()
            product_url = request.POST.get('product_url', '').strip()
            image_url = request.POST.get('image_url', '')
            # Dict chứa lỗi cho từng field
            errors = {}

            if not customer_codes_str:
                errors["customer_codes"] = "取引先コードは必須です。"

            if not delivery_datetime_str:
                errors["delivery_datetime"] = "配信日付は必須です。"

            if not format_type:
                errors["format_type"] = "フォーマットは必須です。"

            if not product_code:
                errors["product_code"] = "商品コードは必須です。"

            if not management_title:
                errors["management_title"] = "管理用タイトルは必須です。"

            if not product_title:
                errors["product_title"] = "商品名は必須です。"

            if not product_price:
                errors["product_price"] = "価格は必須です。"

            if not product_description:
                errors["product_description"] = "商品説明は必須です。"

            if not product_url:
                errors["product_url"] = "商品URLは必須です。"

            # Nếu có lỗi thì render lại form (giữ giá trị cũ)
            if errors:
                products = ProductItem.objects.all()
                return render(request, "create_message_line.html", {
                    "errors": errors,
                    "values": {"customer_codes": customer_codes_str,
                               "delivery_datetime": delivery_datetime_str,
                               "format_type": format_type,
                               "product_code": product_code,
                               "management_title": management_title,
                               "product_title": product_title,
                               "product_price": product_price,
                               "product_description": product_description,
                               "product_url": product_url,
                               "image_url": image_url,
                               },
                    'timestamp': int(now().timestamp()),
                    'products': products
                })
            # Parse delivery datetime
            from datetime import datetime
            delivery_datetime = None
            if delivery_datetime_str:
                try:
                    # Parse datetime string (format: 2025-08-15T20:00)
                    delivery_datetime = datetime.strptime(delivery_datetime_str, '%Y-%m-%dT%H:%M')
                except ValueError:
                    try:
                        # Try alternative format with seconds
                        delivery_datetime = datetime.strptime(delivery_datetime_str, '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        messages.error(request, '配信日付の形式が正しくありません')
                        return redirect('create_message_line')

            # Create LineMessageSetting record
            line_message = LineMessageSetting.objects.create(
                delivery_datetime=delivery_datetime,
                format_type=format_type,
                product_code=product_code,
                management_title=management_title,
                product_title=product_title,
                product_price=product_price,
                product_description=product_description,
                product_url=product_url,
                login_account_id=request.session.get('sso_user')['loginId'],
                image_url=image_url
            )

            # Parse customer codes and create CustomerCode records
            if customer_codes_str:
                # Split by comma and clean up whitespace
                customer_codes = [code.strip() for code in customer_codes_str.split(',') if code.strip()]

                # Create CustomerCode records
                for code in customer_codes:
                    CustomerCode.objects.create(
                        line_message=line_message,
                        code=code
                    )

            messages.success(request, f'LINE メッセージ設定が正常に作成されました。ID: {line_message.id}')
            return redirect('line_dashboard')

        except Exception as e:
            # Handle errors
            messages.error(request, f'エラーが発生しました: {str(e)}')
            return redirect('line_dashboard')

    # Handle GET request (existing functionality)
    context = {
        'timestamp': int(now().timestamp())
    }
    return render(request, 'line_dashboard.html', context)


def update_message_line(request, message_id):
    """
    Update LINE message setting
    """
    if request.method == 'POST':
        try:
            # Get the existing LineMessageSetting
            line_message = get_object_or_404(LineMessageSetting, id=message_id)



            # Get data from POST request
            customer_codes_str = request.POST.get('customer_codes', '').strip()
            delivery_datetime_str = request.POST.get('delivery_datetime', '').strip()
            management_title = request.POST.get('management_title', '').strip()
            format_type = request.POST.get('format_type', '').strip()
            product_code = request.POST.get('product_code', '').strip()
            product_title = request.POST.get('product_title', '').strip()
            product_price = request.POST.get('product_price', '').strip()
            product_description = request.POST.get('product_description', '').strip()
            product_url = request.POST.get('product_url', '').strip()
            image_url = request.POST.get('image_url', '')
            line_message.management_title = request.POST.get('management_title', '')
            line_message.product_code = request.POST.get('product_code', '')
            line_message.product_title = request.POST.get('product_title', '')
            line_message.product_price = request.POST.get('product_price', '')
            line_message.product_description = request.POST.get('product_description', '')
            line_message.product_url = request.POST.get('product_url', '')
            line_message.image_url = request.POST.get('image_url', '')
            errors = {}
            if not customer_codes_str:
                errors["customer_codes"] = "取引先コードは必須です。"
            if not delivery_datetime_str:
                errors["delivery_datetime"] = "配信日付は必須です。"
            if not format_type:
                errors["format_type"] = "フォーマットは必須です。"

            if not product_code:
                errors["product_code"] = "商品コードは必須です。"

            if not management_title:
                errors["management_title"] = "管理用タイトルは必須です。"

            if not product_title:
                errors["product_title"] = "商品名は必須です。"

            if not product_price:
                errors["product_price"] = "価格は必須です。"

            if not product_description:
                errors["product_description"] = "商品説明は必須です。"

            if not product_url:
                errors["product_url"] = "商品URLは必須です。"
            if errors:
                return render(request, 'detail_message_line.html', {
                    "errors": errors,
                    "line_message": line_message,
                    "customer_codes_str": customer_codes_str,
                    "delivery_datetime": delivery_datetime_str,
                    "management_title": management_title,
                    "image": image_url,
                    "timestamp": int(now().timestamp())
                })

            # Parse delivery datetime
            from datetime import datetime
            delivery_datetime = None
            if delivery_datetime_str:
                try:
                    delivery_datetime = datetime.strptime(delivery_datetime_str, '%Y-%m-%dT%H:%M:%S')
                except ValueError:
                    try:
                        delivery_datetime = datetime.strptime(delivery_datetime_str, '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        messages.error(request, '配信日付の形式が正しくありません')
                        return redirect('edit_message_line', message_id=message_id)

            # Update LineMessageSetting
            line_message.delivery_datetime = delivery_datetime
            line_message.management_title = management_title
            line_message.format_type = format_type
            line_message.product_code = product_code
            line_message.product_title = product_title
            line_message.product_price = product_price
            line_message.product_description = product_description
            line_message.product_url = product_url
            line_message.image_url = image_url
            if management_title:
                line_message.management_title = management_title
            else:
                line_message.management_title = f"LINE Message - {product_code}"
            line_message.save()

            # Update customer codes
            if customer_codes_str:
                # Delete existing customer codes
                line_message.customer_codes.all().delete()

                # Split by comma and clean up whitespace
                customer_codes = [code.strip() for code in customer_codes_str.split(',') if code.strip()]

                # Create new CustomerCode records
                for code in customer_codes:
                    CustomerCode.objects.create(
                        line_message=line_message,
                        code=code
                    )

            messages.success(request, f'LINE メッセージ設定が正常に更新されました。ID: {line_message.id}')
            return redirect('line_dashboard')

        except Exception as e:
            messages.error(request, f'エラーが発生しました: {str(e)}')
            return redirect('edit_message_line', message_id=message_id)

    # Handle GET request - show edit form
    try:
        line_message = get_object_or_404(LineMessageSetting, id=message_id)
        customer_codes = line_message.customer_codes.all()
        customer_codes_str = ', '.join([cc.code for cc in customer_codes])
        # Get all products for dropdown
        products = ProductItem.objects.all()

        context = {
            'products': products,
            'line_message': line_message,
            'customer_codes_str': customer_codes_str,
            'image': settings.URL_SMART_ORDER + '/001/img/001/' + line_message.product_code + '_1.jpg',
            'timestamp': int(now().timestamp())
        }
        return render(request, 'detail_message_line.html', context)

    except Exception as e:
        messages.error(request, f'エラーが発生しました: {str(e)}')
        return redirect('line_dashboard')


def delete_message_line(request, message_id):
    if request.method == 'DELETE':
        try:
            # Get the existing LineMessageSetting
            line_message = get_object_or_404(LineMessageSetting, id=message_id)
            line_message.delete()
            return JsonResponse({'message': 'LineMessageSetting deleted successfully'}, status=200)
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

class LineMessageSettingListAPI(APIView):
    def get(self, request):
        line_messages = LineMessageSetting.objects.filter(deleted_at__isnull=True)
        # Search
        search_query = request.GET.get('search_query', '')
        status_filter  = request.GET.get('status_filter', '')
        if search_query:
            line_messages = line_messages.filter(
                Q(management_title__icontains=search_query)
            )
        if status_filter:
            line_messages = line_messages.filter(status=status_filter)
        sort_by = request.GET.get('sort_by','id')  # Mặc định sort theo created_at
        sort_order = request.GET.get('sort_order', 'ASC')
            # Áp dụng sắp xếp
        order_field = f'-{sort_by}' if sort_order == 'desc' else sort_by
        line_messages = line_messages.order_by(order_field)

        # Pagination
        paginator = Paginator(line_messages, 10 )
        page = request.GET.get('page')
        try:
            line_messages = paginator.page(page)
        except PageNotAnInteger:
            line_messages = paginator.page(1)
        except line_messages:
            line_messages = paginator.page(paginator.num_pages)

        # Format data
        tokyo_tz = pytz.timezone("Asia/Tokyo")
        data = []
        for line_message in line_messages:
            data.append({
                "id": line_message.id,
                "delivery_datetime": line_message.delivery_datetime.strftime("%Y/%m/%d %H:%M:%S"),
                "management_title": line_message.management_title,
                "customer_codes_count": line_message.customer_codes.count(),
                "login_account_id": line_message.login_account_id,
                "updated_at": line_message.updated_at.strftime("%Y/%m/%d %H:%M:%S"),
                "status": line_message.status,
            })

        return Response({
            "results": data,
            "pagination": {
                "page": line_messages.number,
                "num_pages": paginator.num_pages,
                "has_next": line_messages.has_next(),
                "has_previous": line_messages.has_previous(),
                "next_page_number": line_messages.next_page_number() if line_messages.has_next() else None,
                "previous_page_number": line_messages.previous_page_number() if line_messages.has_previous() else None,
            }
        }, status=status.HTTP_200_OK)

class UploadImageView(APIView):
    def post(self, request, *args, **kwargs):
        serializer = UploadImageSerializer(data=request.data)
        if serializer.is_valid():
            image = serializer.validated_data['image']
            name, ext = os.path.splitext(image.name)
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            new_filename = f"{name}_{timestamp}{ext}"
            save_path = os.path.join(settings.UPLOAD_IMAGE_PATH, new_filename)

            # create folder if not exist
            os.makedirs(settings.UPLOAD_IMAGE_PATH, exist_ok=True)

            # save  file
            with open(save_path, 'wb+') as f:
                for chunk in image.chunks():
                    f.write(chunk)
            file_url = f"{settings.APP_URL}/static/image/upload/{new_filename}"
            return Response({
                "message": "Upload file successfully",
                "file_url":file_url
            }, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ScheduledMessageAPIView(APIView):
    """
    API View for managing scheduled LINE messages
    """

    def get(self, request, message_id=None):
        """Get scheduled message(s) status"""
        if message_id:
            # Get specific message
            try:
                message = LineMessageSetting.objects.get(id=message_id, deleted_at__isnull=True)
                data = {
                    'id': message.id,
                    'management_title': message.management_title,
                    'delivery_datetime': message.delivery_datetime.isoformat() if message.delivery_datetime else None,
                    'status': message.status,
                    'product_code': message.product_code,
                    'product_title': message.product_title,
                    'customer_codes_count': message.customer_codes.count(),
                    'sent_at': message.sent_at.isoformat() if message.sent_at else None,
                    'error_message': message.error_message,
                    'retry_count': message.retry_count,
                    'is_due': message.is_due,
                    'can_be_sent': message.can_be_sent,
                    'is_completed': message.is_completed,
                    'created_at': message.created_at.isoformat(),
                    'updated_at': message.updated_at.isoformat(),
                }
                return Response(data, status=status.HTTP_200_OK)
            except LineMessageSetting.DoesNotExist:
                return Response(
                    {'error': f'Message with ID {message_id} not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
        else:
            # Get all messages with filtering
            messages = LineMessageSetting.objects.filter(deleted_at__isnull=True)

            # Apply filters
            status_filter = request.GET.get('status')
            if status_filter:
                messages = messages.filter(status=status_filter)

            due_only = request.GET.get('due_only', '').lower() == 'true'
            if due_only:
                messages = messages.filter(
                    delivery_datetime__lte=timezone.now(),
                    status='配信予定'
                )

            # Pagination
            paginator = Paginator(messages.order_by('-created_at'), 20)
            page = request.GET.get('page', 1)
            try:
                messages_page = paginator.page(page)
            except PageNotAnInteger:
                messages_page = paginator.page(1)
            except EmptyPage:
                messages_page = paginator.page(paginator.num_pages)

            data = []
            for message in messages_page:
                data.append({
                    'id': message.id,
                    'management_title': message.management_title,
                    'delivery_datetime': message.delivery_datetime.isoformat() if message.delivery_datetime else None,
                    'status': message.status,
                    'product_code': message.product_code,
                    'customer_codes_count': message.customer_codes.count(),
                    'is_due': message.is_due,
                    'created_at': message.created_at.isoformat(),
                })

            return Response({
                'messages': data,
                'pagination': {
                    'current_page': messages_page.number,
                    'total_pages': paginator.num_pages,
                    'total_count': paginator.count,
                    'has_next': messages_page.has_next(),
                    'has_previous': messages_page.has_previous(),
                }
            }, status=status.HTTP_200_OK)

    def post(self, request, message_id=None):
        """Trigger manual send of scheduled message(s)"""
        if message_id:
            # Send specific message
            try:
                message = LineMessageSetting.objects.get(id=message_id, deleted_at__isnull=True)

                if not message.can_be_sent:
                    return Response(
                        {'error': f'Message cannot be sent. Current status: {message.status}'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Queue the message for sending
                from .tasks import send_line_message_task
                task_result = send_line_message_task.delay(message.id)

                return Response({
                    'message': f'Message {message_id} queued for sending',
                    'task_id': task_result.id,
                    'status': 'queued'
                }, status=status.HTTP_200_OK)

            except LineMessageSetting.DoesNotExist:
                return Response(
                    {'error': f'Message with ID {message_id} not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
        else:
            # Send all due messages
            from .tasks import check_and_send_scheduled_messages
            task_result = check_and_send_scheduled_messages.delay()

            return Response({
                'message': 'Scheduled messages check queued',
                'task_id': task_result.id,
                'status': 'queued'
            }, status=status.HTTP_200_OK)

    def patch(self, request, message_id):
        """Update message status (cancel, etc.)"""
        try:
            message = LineMessageSetting.objects.get(id=message_id, deleted_at__isnull=True)

            action = request.data.get('action')

            if action == 'cancel':
                if message.cancel():
                    return Response({
                        'message': f'Message {message_id} cancelled successfully',
                        'status': message.status
                    }, status=status.HTTP_200_OK)
                else:
                    return Response(
                        {'error': f'Cannot cancel message. Current status: {message.status}'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            else:
                return Response(
                    {'error': 'Invalid action. Supported actions: cancel'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        except LineMessageSetting.DoesNotExist:
            return Response(
                {'error': f'Message with ID {message_id} not found'},
                status=status.HTTP_404_NOT_FOUND
            )