from django.core.management.base import BaseCommand
from django.utils import timezone
from myapp.models import LineMessageSetting
from myapp.tasks import check_and_send_scheduled_messages


class Command(BaseCommand):
    help = 'Manually check and send scheduled LINE messages'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be sent without actually sending'
        )
        parser.add_argument(
            '--message-id',
            type=int,
            help='Send specific message by ID'
        )

    def handle(self, *args, **options):
        now = timezone.now()
        
        if options['message_id']:
            # Send specific message
            try:
                message = LineMessageSetting.objects.get(id=options['message_id'])
                self.stdout.write(f'Processing message ID: {message.id}')
                self.stdout.write(f'Title: {message.management_title}')
                self.stdout.write(f'Delivery time: {message.delivery_datetime}')
                self.stdout.write(f'Status: {message.status}')
                
                if not options['dry_run']:
                    from myapp.tasks import send_line_message_task
                    result = send_line_message_task.delay(message.id)
                    self.stdout.write(
                        self.style.SUCCESS(f'Message queued for sending. Task ID: {result.id}')
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING('DRY RUN: Message would be sent')
                    )
                    
            except LineMessageSetting.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Message with ID {options["message_id"]} not found')
                )
            return

        # Check all due messages
        due_messages = LineMessageSetting.objects.filter(
            delivery_datetime__lte=now,
            status='配信予定',
            deleted_at__isnull=True
        )
        
        self.stdout.write(f'Current time: {now}')
        self.stdout.write(f'Found {due_messages.count()} messages due for sending:')
        
        if not due_messages.exists():
            self.stdout.write(self.style.SUCCESS('No messages due for sending'))
            return
        
        for message in due_messages:
            self.stdout.write(f'\n--- Message ID: {message.id} ---')
            self.stdout.write(f'Title: {message.management_title}')
            self.stdout.write(f'Product: {message.product_code} - {message.product_title}')
            self.stdout.write(f'Delivery time: {message.delivery_datetime}')
            self.stdout.write(f'Customer codes: {message.customer_codes.count()}')
            
            # Show customer codes
            customer_codes = list(message.customer_codes.values_list('code', flat=True))
            if customer_codes:
                self.stdout.write(f'Recipients: {", ".join(customer_codes)}')
        
        if not options['dry_run']:
            # Actually send the messages
            self.stdout.write('\nSending messages...')
            result = check_and_send_scheduled_messages.delay()
            self.stdout.write(
                self.style.SUCCESS(f'Scheduled messages task queued. Task ID: {result.id}')
            )
        else:
            self.stdout.write(
                self.style.WARNING('\nDRY RUN: Messages would be sent')
            )
        
        # Show summary
        self.stdout.write('\n--- Summary ---')
        total_messages = LineMessageSetting.objects.filter(deleted_at__isnull=True).count()
        pending_messages = LineMessageSetting.objects.filter(
            status='配信予定',
            deleted_at__isnull=True
        ).count()
        completed_messages = LineMessageSetting.objects.filter(
            status__in=['配信完了', '一部配信完了'],
            deleted_at__isnull=True
        ).count()
        error_messages = LineMessageSetting.objects.filter(
            status='エラー',
            deleted_at__isnull=True
        ).count()
        
        self.stdout.write(f'Total messages: {total_messages}')
        self.stdout.write(f'Pending: {pending_messages}')
        self.stdout.write(f'Completed: {completed_messages}')
        self.stdout.write(f'Errors: {error_messages}')
