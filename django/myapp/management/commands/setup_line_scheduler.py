from django.core.management.base import BaseCommand
from django_celery_beat.models import PeriodicTask, CrontabSchedule
import json


class Command(BaseCommand):
    help = 'Setup periodic task for LINE message scheduling'

    def add_arguments(self, parser):
        parser.add_argument(
            '--interval',
            type=int,
            default=5,
            help='Interval in minutes to check for scheduled messages (default: 5)'
        )
        parser.add_argument(
            '--delete',
            action='store_true',
            help='Delete existing periodic task'
        )

    def handle(self, *args, **options):
        task_name = 'check-scheduled-line-messages'
        
        if options['delete']:
            # Delete existing task
            deleted_count = PeriodicTask.objects.filter(name=task_name).delete()[0]
            if deleted_count > 0:
                self.stdout.write(
                    self.style.SUCCESS(f'Successfully deleted periodic task: {task_name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'No periodic task found with name: {task_name}')
                )
            return

        interval = options['interval']
        
        # Create or get crontab schedule (every N minutes)
        schedule, created = CrontabSchedule.objects.get_or_create(
            minute=f'*/{interval}',
            hour='*',
            day_of_week='*',
            day_of_month='*',
            month_of_year='*',
        )
        
        if created:
            self.stdout.write(f'Created new crontab schedule: every {interval} minutes')
        else:
            self.stdout.write(f'Using existing crontab schedule: every {interval} minutes')

        # Create or update periodic task
        task, created = PeriodicTask.objects.get_or_create(
            name=task_name,
            defaults={
                'crontab': schedule,
                'task': 'myapp.tasks.check_and_send_scheduled_messages',
                'args': json.dumps([]),
                'kwargs': json.dumps({}),
                'enabled': True,
                'description': 'Check and send scheduled LINE messages',
            }
        )
        
        if created:
            self.stdout.write(
                self.style.SUCCESS(f'Successfully created periodic task: {task_name}')
            )
        else:
            # Update existing task
            task.crontab = schedule
            task.task = 'myapp.tasks.check_and_send_scheduled_messages'
            task.enabled = True
            task.save()
            self.stdout.write(
                self.style.SUCCESS(f'Successfully updated periodic task: {task_name}')
            )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Periodic task "{task_name}" is now set to run every {interval} minutes'
            )
        )
        
        # Display current status
        self.stdout.write('\n--- Current Periodic Tasks ---')
        tasks = PeriodicTask.objects.filter(enabled=True)
        for task in tasks:
            self.stdout.write(f'• {task.name}: {task.task} ({task.crontab})')
