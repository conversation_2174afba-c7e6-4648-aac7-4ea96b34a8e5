from celery import shared_task
from django.utils import timezone
from django.conf import settings
from .models import LineMessageSetting, CustomerCode
import requests
import json
import logging

logger = logging.getLogger(__name__)

@shared_task
def check_and_send_scheduled_messages():
    """
    Celery task to check for scheduled LINE messages that are due to be sent
    and send them automatically.
    """
    try:
        # Get current time
        now = timezone.now()
        
        # Find messages that are due to be sent
        due_messages = LineMessageSetting.objects.filter(
            delivery_datetime__lte=now,
            status='配信予定',  # Only pending messages
            deleted_at__isnull=True
        )
        
        sent_count = 0
        error_count = 0
        
        for message in due_messages:
            try:
                # Send the message
                result = send_line_message_task.delay(message.id)
                sent_count += 1
                logger.info(f"Scheduled message {message.id} queued for sending")
                
            except Exception as e:
                error_count += 1
                logger.error(f"Failed to queue message {message.id}: {str(e)}")
                
                # Update status to error
                message.status = 'エラー'
                message.save()
        
        logger.info(f"Scheduled message check completed. Sent: {sent_count}, Errors: {error_count}")
        return {
            'status': 'completed',
            'sent_count': sent_count,
            'error_count': error_count
        }
        
    except Exception as e:
        logger.error(f"Error in check_and_send_scheduled_messages: {str(e)}")
        return {
            'status': 'error',
            'message': str(e)
        }

@shared_task
def send_line_message_task(message_id):
    """
    Celery task to send a specific LINE message.
    """
    try:
        # Get the message
        message = LineMessageSetting.objects.get(id=message_id)
        
        # Check if message is still pending
        if message.status != '配信予定':
            logger.warning(f"Message {message_id} is not in pending status: {message.status}")
            return {
                'status': 'skipped',
                'message': f'Message status is {message.status}'
            }
        
        # Update status to sending
        message.status = '配信中'
        message.save()
        
        # Get customer codes
        customer_codes = message.customer_codes.all()
        
        if not customer_codes.exists():
            logger.warning(f"No customer codes found for message {message_id}")
            message.status = 'エラー'
            message.save()
            return {
                'status': 'error',
                'message': 'No customer codes found'
            }
        
        # Prepare LINE message data
        line_message_data = prepare_line_message_data(message)
        
        # Send to each customer
        success_count = 0
        error_count = 0
        
        for customer_code in customer_codes:
            try:
                # Send LINE message to customer
                result = send_to_line_api(customer_code.code, line_message_data)
                
                if result.get('success'):
                    success_count += 1
                    logger.info(f"Message sent successfully to customer {customer_code.code}")
                else:
                    error_count += 1
                    logger.error(f"Failed to send message to customer {customer_code.code}: {result.get('error')}")
                    
            except Exception as e:
                error_count += 1
                logger.error(f"Exception sending to customer {customer_code.code}: {str(e)}")
        
        # Update message status based on results
        if error_count == 0:
            message.status = '配信完了'
        elif success_count > 0:
            message.status = '一部配信完了'
        else:
            message.status = 'エラー'
        
        message.save()
        
        logger.info(f"Message {message_id} sending completed. Success: {success_count}, Errors: {error_count}")
        
        return {
            'status': 'completed',
            'message_id': message_id,
            'success_count': success_count,
            'error_count': error_count,
            'final_status': message.status
        }
        
    except LineMessageSetting.DoesNotExist:
        logger.error(f"Message {message_id} not found")
        return {
            'status': 'error',
            'message': f'Message {message_id} not found'
        }
    except Exception as e:
        logger.error(f"Error in send_line_message_task for message {message_id}: {str(e)}")
        
        # Try to update status to error
        try:
            message = LineMessageSetting.objects.get(id=message_id)
            message.status = 'エラー'
            message.save()
        except:
            pass
            
        return {
            'status': 'error',
            'message': str(e)
        }

def prepare_line_message_data(message):
    """
    Prepare LINE message data based on format type and message content.
    """
    base_data = {
        'product_code': message.product_code,
        'product_title': message.product_title,
        'product_price': message.product_price,
        'product_description': message.product_description,
        'product_url': message.product_url,
        'image_url': message.image_url,
        'format_type': message.format_type
    }
    
    # Add format-specific data based on format_type
    if message.format_type == 'flex':
        # Prepare Flex Message format
        return prepare_flex_message(base_data)
    elif message.format_type == 'carousel':
        # Prepare Carousel format
        return prepare_carousel_message(base_data)
    else:
        # Default text message
        return prepare_text_message(base_data)

def prepare_flex_message(data):
    """
    Prepare Flex Message format for LINE.
    """
    return {
        'type': 'flex',
        'altText': data['product_title'],
        'contents': {
            'type': 'bubble',
            'hero': {
                'type': 'image',
                'url': data['image_url'] or 'https://via.placeholder.com/300x200',
                'size': 'full',
                'aspectRatio': '20:13',
                'aspectMode': 'cover'
            },
            'body': {
                'type': 'box',
                'layout': 'vertical',
                'contents': [
                    {
                        'type': 'text',
                        'text': data['product_title'],
                        'weight': 'bold',
                        'size': 'xl'
                    },
                    {
                        'type': 'text',
                        'text': data['product_price'],
                        'size': 'md',
                        'color': '#999999'
                    },
                    {
                        'type': 'text',
                        'text': data['product_description'],
                        'size': 'sm',
                        'wrap': True
                    }
                ]
            },
            'footer': {
                'type': 'box',
                'layout': 'vertical',
                'contents': [
                    {
                        'type': 'button',
                        'action': {
                            'type': 'uri',
                            'label': '詳細を見る',
                            'uri': data['product_url']
                        }
                    }
                ]
            }
        }
    }

def prepare_carousel_message(data):
    """
    Prepare Carousel format for LINE.
    """
    return {
        'type': 'template',
        'altText': data['product_title'],
        'template': {
            'type': 'carousel',
            'columns': [
                {
                    'thumbnailImageUrl': data['image_url'] or 'https://via.placeholder.com/300x200',
                    'title': data['product_title'],
                    'text': data['product_description'],
                    'actions': [
                        {
                            'type': 'uri',
                            'label': '詳細を見る',
                            'uri': data['product_url']
                        }
                    ]
                }
            ]
        }
    }

def prepare_text_message(data):
    """
    Prepare simple text message format.
    """
    message_text = f"""
{data['product_title']}

{data['product_price']}

{data['product_description']}

詳細: {data['product_url']}
    """.strip()
    
    return {
        'type': 'text',
        'text': message_text
    }

def send_to_line_api(customer_code, message_data):
    """
    Send message to LINE API for specific customer.
    This is a placeholder - you need to implement actual LINE API integration.
    """
    try:
        # TODO: Replace with actual LINE API endpoint and authentication
        line_api_url = getattr(settings, 'LINE_API_URL', 'https://api.line.me/v2/bot/message/push')
        line_access_token = getattr(settings, 'LINE_ACCESS_TOKEN', '')
        
        if not line_access_token:
            logger.error("LINE_ACCESS_TOKEN not configured")
            return {'success': False, 'error': 'LINE_ACCESS_TOKEN not configured'}
        
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {line_access_token}'
        }
        
        # Prepare payload for LINE API
        payload = {
            'to': customer_code,  # This should be LINE user ID, not customer code
            'messages': [message_data]
        }
        
        # For now, just log the message (replace with actual API call)
        logger.info(f"Would send LINE message to {customer_code}: {json.dumps(payload, ensure_ascii=False)}")
        
        # TODO: Uncomment when ready to send actual messages
        # response = requests.post(line_api_url, headers=headers, json=payload, timeout=30)
        # 
        # if response.status_code == 200:
        #     return {'success': True}
        # else:
        #     return {'success': False, 'error': f'HTTP {response.status_code}: {response.text}'}
        
        # For testing, always return success
        return {'success': True}
        
    except Exception as e:
        logger.error(f"Exception in send_to_line_api: {str(e)}")
        return {'success': False, 'error': str(e)}
