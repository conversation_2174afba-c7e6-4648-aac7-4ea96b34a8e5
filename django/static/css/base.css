/*
* base.css
* File include item base only specific css only
******************************************************************************/

.menu .app-brand.base {
  height: 64px;
  margin-top: 12px;
}

.app-brand-logo.base svg {
  width: 22px;
  height: 38px;
}

.app-brand-text.base {
  font-size: 1.75rem;
  letter-spacing: -0.5px;
}

/* ! For .layout-navbar-fixed added fix padding top tpo .layout-page */
/* Detached navbar */
.layout-navbar-fixed .layout-wrapper:not(.layout-horizontal):not(.layout-without-menu) .layout-page {
  padding-top: 76px !important;
}
/* Default navbar */
.layout-navbar-fixed .layout-wrapper:not(.layout-without-menu) .layout-page {
  padding-top: 64px !important;
}

/* Navbar page z-index issue solution */
.content-wrapper .navbar {
  z-index: auto;
}

/*
* Content
******************************************************************************/

.base-blocks > * {
  display: block !important;
}

.base-inline-spacing > * {
  margin: 1rem 0.375rem 0 0 !important;
}

/* ? .base-vertical-spacing class is used to have vertical margins between elements. To remove margin-top from the first-child, use .base-only-element class with .base-vertical-spacing class. For example, we have used this class in forms-input-groups.html file. */
.base-vertical-spacing > * {
  margin-top: 1rem !important;
  margin-bottom: 0 !important;
}
.base-vertical-spacing.base-only-element > :first-child {
  margin-top: 0 !important;
}

.base-vertical-spacing-lg > * {
  margin-top: 1.875rem !important;
  margin-bottom: 0 !important;
}
.base-vertical-spacing-lg.base-only-element > :first-child {
  margin-top: 0 !important;
}

.base-vertical-spacing-xl > * {
  margin-top: 5rem !important;
  margin-bottom: 0 !important;
}
.base-vertical-spacing-xl.base-only-element > :first-child {
  margin-top: 0 !important;
}

.rtl-only {
  display: none !important;
  text-align: left !important;
  direction: ltr !important;
}

[dir='rtl'] .rtl-only {
  display: block !important;
}

/*
* Layout base
******************************************************************************/

.layout-base-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-top: 1rem;
}
.layout-base-placeholder img {
  width: 900px;
}
.layout-base-info {
  text-align: center;
  margin-top: 1rem;
}
.hover-underline {
  text-decoration: none;
  transition: text-decoration 0.2s ease-in-out;
}
.hover-underline:hover {
  text-decoration: underline;
}
.img-hover-zoom {
  transition: transform 0.3s ease;
}
.img-hover-zoom:hover {
  transform: scale(1.1);
}
.min-vh-450 {
  min-height: 450px;
}
.preview-card {
  max-width: 450px;
  background-color: #e0ebf7; /* nền xanh nhạt */
  padding: 1rem;
  border-radius: 1rem;
}

.preview-img {
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.btn-custom {
  background-color: #a15c35; /* nâu */
  color: #fff;
  border-radius: 0.75rem;
  padding: 0.6rem;
  font-weight: bold;
}

.btn-custom:hover {
  background-color: #8c4f2d;
  color: #fff;
}
.select_product:hover{
  transform:translate(0px) !important;
}
.upload-box {
      width: 400px;
      height: 200px;
      border: 2px dashed #aaa;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      color: #666; cursor: pointer;
      transition: border-color 0.3s, background-color 0.3s;
      margin: 20px auto; text-align: center;
      overflow: hidden;
      position: relative;
    }
    .upload-box.dragover {
      border-color: #333;
      background-color: #f9f9f9;
    }
    #preview {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;   /* căn giữa theo chiều dọc */
      justify-content: center; /* căn giữa theo chiều ngang */
    }
    .upload-box img {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain; /* giữ tỷ lệ, không bị cắt */
      display: block;
    }
    input[type="file"] { display: none; }
    input#image_url { display: none; }