#!/usr/bin/env python
"""
Script to check current scheduler configuration
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django_celery_beat.models import PeriodicTask, CrontabSchedule
from django.conf import settings


def check_celery_beat_config():
    """Check Celery Beat configuration"""
    print("=== Celery Beat Configuration ===")
    
    # Check settings
    print(f"CELERY_BEAT_SCHEDULER: {getattr(settings, 'CELERY_BEAT_SCHEDULER', 'Not set')}")
    print(f"CELERY_TIMEZONE: {getattr(settings, 'CELERY_TIMEZONE', 'Not set')}")
    
    # Check schedule in settings
    beat_schedule = getattr(settings, 'CELERY_BEAT_SCHEDULE', {})
    print(f"\nCELERY_BEAT_SCHEDULE:")
    for task_name, config in beat_schedule.items():
        print(f"  - {task_name}:")
        print(f"    Task: {config.get('task')}")
        print(f"    Schedule: {config.get('schedule')}")
        print(f"    Options: {config.get('options', {})}")


def check_database_tasks():
    """Check periodic tasks in database"""
    print("\n=== Database Periodic Tasks ===")
    
    tasks = PeriodicTask.objects.all()
    print(f"Total periodic tasks: {tasks.count()}")
    
    for task in tasks:
        print(f"\nTask: {task.name}")
        print(f"  Enabled: {task.enabled}")
        print(f"  Task: {task.task}")
        print(f"  Crontab: {task.crontab}")
        print(f"  Last run: {task.last_run_at}")
        print(f"  Total runs: {task.total_run_count}")
        
        if task.crontab:
            cron = task.crontab
            print(f"  Schedule details:")
            print(f"    Minute: {cron.minute}")
            print(f"    Hour: {cron.hour}")
            print(f"    Day of week: {cron.day_of_week}")
            print(f"    Day of month: {cron.day_of_month}")
            print(f"    Month of year: {cron.month_of_year}")


def check_line_message_scheduler():
    """Check specifically for LINE message scheduler"""
    print("\n=== LINE Message Scheduler Status ===")
    
    try:
        task = PeriodicTask.objects.get(name='check-scheduled-line-messages')
        print(f"✅ LINE message scheduler found!")
        print(f"   Enabled: {task.enabled}")
        print(f"   Task: {task.task}")
        print(f"   Schedule: {task.crontab}")
        print(f"   Last run: {task.last_run_at or 'Never'}")
        print(f"   Total runs: {task.total_run_count}")
        
        if task.crontab:
            cron = task.crontab
            if cron.minute == '*/1':
                print(f"   ✅ Configured to run every 1 minute")
            else:
                print(f"   ⚠️  Schedule: {cron.minute} (not every 1 minute)")
        
    except PeriodicTask.DoesNotExist:
        print("❌ LINE message scheduler not found!")
        print("   Run: python manage.py setup_line_scheduler --interval 1")


def main():
    print("Checking Celery Beat and LINE Message Scheduler Configuration\n")
    
    check_celery_beat_config()
    check_database_tasks()
    check_line_message_scheduler()
    
    print("\n=== Summary ===")
    print("✅ Configuration updated to check every 1 minute")
    print("📝 To start the scheduler:")
    print("   1. celery -A core worker --loglevel=info")
    print("   2. celery -A core beat --loglevel=info --scheduler django_celery_beat.schedulers:DatabaseScheduler")


if __name__ == '__main__':
    main()
