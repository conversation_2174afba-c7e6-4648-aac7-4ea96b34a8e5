{% extends "layout/base.html" %}
{% load static %}

{% block content %}
<div class="container py-4">
    <div class="row g-4">
        <!-- Form -->
        <div class="col-lg-8">
            <div class="card shadow-sm p-4">
                <form method="post" action="{% url 'store_message_line' %}">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label class="form-label">取引先コード (複数ある場合はカンマ区切り)</label>
                        <input type="text" name="customer_codes" class="form-control"
                               value="{{ values.customer_codes|default:'' }}" autocomplete="off">
                        {% if errors.customer_codes %}
                        <div class="invalid-feedback d-block">
                            {{ errors.customer_codes }}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label class="form-label">配信日付</label>
                        <input name="delivery_datetime" id="delivery_datetime" type="text" class="form-control"
                               value="{{ values.delivery_datetime|default:'' }}" autocomplete="off">
                        {% if errors.customer_codes %}
                        <div class="invalid-feedback d-block">
                            {{ errors.delivery_datetime }}
                        </div>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <label class="form-label">管理用タイトル</label>
                        <input name="management_title" id="management_title" type="text" class="form-control"
                               value="{{ values.management_title|default:'' }}" autocomplete="off">
                        {% if errors.management_title %}
                        <div class="invalid-feedback d-block">
                            {{ errors.management_title }}
                        </div>
                        {% endif %}
                    </div>
                    <div class="btn-group w-20 mb-3" role="group" aria-label="選択">
                        <input type="radio" class="btn-check" name="format_type" id="option1" autocomplete="off" checked
                               value="商品選択">
                        <label class="btn btn-outline-secondary select_product" for="option1">商品選択</label>

                        <!--            <input disabled type="radio" class="btn-check" name="format_type" id="option2" autocomplete="off" value="カスタム">-->
                        <!--            <label class="btn btn-outline-secondary select_product"  for="option2">カスタム</label>-->
                    </div>

                    <div class="row mb-3">
                        <div class="col-9">
                            <input
                                    id="product-code-input"
                                    name="product_code"
                                    autocomplete="off"
                                    class="form-control"
                                    list="datalistOptions"
                                    placeholder="商品コードを入力する"
                                    value="{{ values.product_code|default:'' }}"
                            />
                            <datalist id="datalistOptions">
                                {% for product in products %}
                                <option value="{{product.product_code}}"></option>
                                {% endfor %}
                            </datalist>
                            {% if errors.product_code %}
                            <div class="invalid-feedback d-block">
                                {{ errors.product_code }}
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-3">
                            <button id="get-product-info" class="btn btn-outline-primary w-100" type="button">情報取得
                            </button>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label d-block">画像(画像変更する場合は画像をクリックしてください)</label>
                        <input type="text" id="image_url" name="image_url" value="{{ values.image_url|default:'' }}">
                        <div class="upload-box" id="drop-area">
                          <div id="preview">
                            <p >画像をドラッグまたはクリックして選択</p>
                          </div>
                          <input type="file" id="fileElem" accept="image/*">
                        </div>
<!--                        <img data-images="{{ values.product_image|default:'' }}" width="400" height="200"-->
<!--                             id="product-image" src="{% static 'image/preview_default.png' %}" alt="preview"-->
<!--                             class="preview-image img-fluid rounded">-->
                    </div>

                    <div class="mb-3">
                        <label class="form-label">タイトル</label>
                        <input name="product_title" id="product-title" type="text" class="form-control"
                               value="{{ values.product_title|default:'' }}" autocomplete="off">
                        {% if errors.customer_codes %}
                        <div class="invalid-feedback d-block">
                            {{ errors.product_title }}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label class="form-label">中見出し</label>
                        <input name="product_price" id="product-price" type="text" class="form-control"
                               value="{{ values.product_price|default:'' }}" autocomplete="off">
                        {% if errors.customer_codes %}
                        <div class="invalid-feedback d-block">
                            {{ errors.product_price }}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label class="form-label">テキスト</label>
                        <textarea autocomplete="off" name="product_description" id="product-description"
                                  class="form-control" rows="2">{{ values.product_description|default:'' }}</textarea>
                        {% if errors.customer_codes %}
                        <div class="invalid-feedback d-block">
                            {{ errors.product_description }}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label class="form-label">遷移先URL</label>
                        <input autocomplete="off" name="product_url" id="product-url" type="text" class="form-control"
                               value="{{ values.product_url|default:'' }}">
                        {% if errors.customer_codes %}
                        <div class="invalid-feedback d-block">
                            {{ errors.product_url }}
                        </div>
                        {% endif %}
                    </div>
                    <div>
                        <button id="btn_create" type="submit" class="btn btn-primary  w-100">送信</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Preview -->
        <div class="col-lg-4">
            <h6 class="mb-2">プレビュー</h6>
            <div class="card preview-card shadow-sm">
                <img class="preview-image" id="preview-image" src="{% static 'image/preview_default.png' %}"
                     alt="商品画像">
                <div class="card-body">
                    <h5 id="preview-title" class="card-title">{{ values.product_title|default:'' }}</h5>
                    <p id="preview-price" class="card-text text-muted">{{ values.product_price|default:'' }}</p>
                    <p id="preview-description" class="small text-muted">{{ values.product_description|default:'' }}</p>
                    <a href="#" class="btn btn-primary w-100">商品を見る</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
    {% block extra_js%}
    <script>
        $(document).ready(function () {
            flatpickr("#delivery_datetime", {
                enableTime: true,                // bật chọn giờ
                dateFormat: "Y-m-d H:i:S",       // format theo Django
                time_24hr: true,                 // giờ 24h
                enableSeconds: true,          // bật giây
                allowInput: true,
                locale: "ja",
            });

            $('#get-product-info').click(function () {
                const productCode = $('#product-code-input').val().trim();

                if (!productCode) {
                    alert('商品コードを入力してください');
                    return;
                }

                // Show loading state
                $(this).prop('disabled', true).text('取得中...');

                $.ajax({
                    url: `/api/products/${productCode}/`,
                    type: 'GET',
                    success: function (data) {
                        $('#product-url').val(data.url);
                        if (data.image !== '') {
                            checkImage(data.image, function (exists) {
                                if (exists) {
                                    preview.html(`<img src="${data.image}" alt="preview">`);
                                    preview_image.attr('src', data.image);
                                    $("#image_url").val(data.image);
                                }
                            });
                        } else {
                            preview.html('');
                            preview_image.attr('src', '/static/image/preview_default.png');
                            $("#image_url").val('');
                        }
                        $('#btn_create').prop('disabled', false);
                    },
                    error: function (xhr, status, error) {
                        if (xhr.status === 404) {
                            alert('商品が見つかりませんでした');
                        } else {
                            alert('エラーが発生しました: ' + error);
                        }
                        $('#product-url').val('');
                        $('#btn_create').prop('disabled', true);
                        // Update preview
                    },
                    complete: function () {
                        // Reset button state
                        $('#get-product-info').prop('disabled', false).text('情報取得');
                    }
                });
            });

            function checkImage(url, callback) {
                var img = new Image();
                img.onload = function () {
                    callback(true);
                };
                img.onerror = function () {
                    callback(false);
                };
                img.src = url;
            }

            $('#product-title').on('input', function () {
                $('#preview-title').text($(this).val());
            });
            $('#product-price').on('input', function () {
                $('#preview-price').text($(this).val());
            });
            $('#product-description').on('input', function () {
                $('#preview-description').text($(this).val());
            });
            checkImage($('#product-image').data('images'), function (exists) {
                if (exists) {
                    $('.preview-image').attr('src', $('#product-image').data('images'));
                }
            });
            $('#product-code-input').on('change', function () {
                $('.preview-image ').attr('src', '/static/image/preview_default.png');
                $('#product-url').val('');
                $('#btn_create').prop('disabled', true);
            });
            const dropArea = $('#drop-area');
            const preview = $('#preview');
            const preview_image = $('#preview-image');
            const fileInput = $('#fileElem');

            // Click khung -> mở file input
            dropArea.on('click', function () {
                fileInput.trigger('click');
            });

            fileInput.on('click', function (e) {
                e.stopPropagation();
            });

            // Drag events
            dropArea.on('dragenter dragover', function (e) {
                e.preventDefault();  // ⚠️ cần cho Firefox
                e.stopPropagation();
                dropArea.addClass('dragover');
            });

            dropArea.on('dragleave', function (e) {
                e.preventDefault();
                e.stopPropagation();
                dropArea.removeClass('dragover');
            });

            dropArea.on('drop', function (e) {
                e.preventDefault();  // ⚠️ cần cho Firefox
                e.stopPropagation();
                dropArea.removeClass('dragover');

                const dt = e.originalEvent.dataTransfer;
                if (dt && dt.files && dt.files.length > 0) {
                    handleFiles(dt.files);
                }
            });

            // Khi chọn file bằng input
            fileInput.on('change', function () {
                handleFiles(this.files);
            });

            function handleFiles(files) {
                if (!files || !files.length) return;
                const file = files[0];
                if (!file.type.startsWith('image/')) {
                    alert('Vui lòng chọn file ảnh!');
                    return;
                }
                const reader = new FileReader();
                reader.onload = function (e) {
                    preview.html(`<img src="${e.target.result}" alt="preview">`);
                    preview_image.attr('src', e.target.result);
                };

                reader.readAsDataURL(file);
                uploadFile(file);
            }

            function uploadFile(file) {
                const formData = new FormData();
                formData.append("image", file);
                $.ajax({
                    url: "/api/upload-image",
                    type: "POST",
                    data: formData,
                    contentType: false,
                    processData: false,
                    success: function (res) {
                        preview.html(`<img src="${res.file_url}" alt="preview">`);
                        preview_image.attr('src', res.file_url);
                        $("#image_url").val(res.file_url);

                        console.log("Server trả về:", res);
                    },
                    error: function (err) {
                        console.error("Lỗi upload:", err);
                    }
                });
            }
        });


    </script>
    {% endblock %}