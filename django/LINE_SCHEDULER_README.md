# LINE Message Scheduling System

Hệ thống tự động gửi LINE message theo lịch trình đã được triển khai thành công.

## Tính năng chính

1. **Tự động gửi message theo thời gian**: <PERSON><PERSON> thống sẽ tự động kiểm tra và gửi message khi `delivery_datetime` đến hạn
2. **Quản lý trạng thái**: <PERSON> dõi trạng thái của từng message (配信予定, 配信中, 配信完了, エラー, etc.)
3. **API endpoints**: Cung cấp API để quản lý và trigger manual send
4. **Management commands**: Commands để setup và test hệ thống
5. **Logging**: Ghi log chi tiết cho việc debug và monitoring

## Cấu trúc hệ thống

### 1. Model Updates (LineMessageSetting)

<PERSON><PERSON> thêm các field mới:
- `sent_at`: <PERSON>h<PERSON><PERSON> gian thực tế gửi message
- `error_message`: <PERSON><PERSON><PERSON> thông tin lỗi nếu có
- `retry_count`: <PERSON><PERSON> lần retry
- `status`: <PERSON><PERSON><PERSON> thiện với choices và các trạng thái mới

Các method mới:
- `is_due`: Kiểm tra message có đến hạn gửi không
- `can_be_sent`: Kiểm tra message có thể gửi được không
- `mark_as_sending()`, `mark_as_completed()`, `mark_as_error()`: Quản lý trạng thái
- `cancel()`: Hủy message đã lên lịch

### 2. Celery Tasks

**File**: `myapp/tasks.py`

- `check_and_send_scheduled_messages`: Task chính để kiểm tra và gửi message theo lịch
- `send_line_message_task`: Task gửi một message cụ thể
- Các helper functions để prepare message data theo format (flex, carousel, text)

### 3. API Endpoints

**Base URL**: `/api/scheduled-messages/`

- `GET /api/scheduled-messages/`: Lấy danh sách messages với pagination và filter
- `GET /api/scheduled-messages/{id}/`: Lấy thông tin chi tiết một message
- `POST /api/scheduled-messages/{id}/send/`: Trigger manual send một message
- `POST /api/scheduled-messages/send-all/`: Trigger send tất cả messages đến hạn
- `PATCH /api/scheduled-messages/{id}/`: Update message (cancel, etc.)

### 4. Management Commands

**Setup periodic task**:
```bash
python manage.py setup_line_scheduler --interval 5
```

**Manual check và send**:
```bash
# Dry run
python manage.py check_line_messages --dry-run

# Send specific message
python manage.py check_line_messages --message-id 123

# Send all due messages
python manage.py check_line_messages
```

## Cài đặt và cấu hình

### 1. Chạy migrations
```bash
python manage.py migrate
```

### 2. Setup Celery Beat periodic task
```bash
python manage.py setup_line_scheduler --interval 5
```

### 3. Cấu hình environment variables
```bash
# Redis URL cho Celery
REDIS_URL=redis://localhost:6379/0

# LINE API configuration
LINE_API_URL=https://api.line.me/v2/bot/message/push
LINE_ACCESS_TOKEN=your_line_access_token_here
```

### 4. Khởi động Celery services

**Terminal 1 - Celery Worker**:
```bash
cd django
celery -A core worker --loglevel=info
```

**Terminal 2 - Celery Beat**:
```bash
cd django
celery -A core beat --loglevel=info --scheduler django_celery_beat.schedulers:DatabaseScheduler
```

**Terminal 3 - Django Server**:
```bash
cd django
python manage.py runserver
```

## Cách sử dụng

### 1. Tạo scheduled message qua web interface
- Truy cập LINE dashboard
- Tạo message với `delivery_datetime` trong tương lai
- Hệ thống sẽ tự động gửi khi đến thời gian

### 2. Sử dụng API

**Lấy danh sách messages đến hạn**:
```bash
curl -X GET "http://localhost:8000/api/scheduled-messages/?due_only=true"
```

**Trigger manual send**:
```bash
curl -X POST "http://localhost:8000/api/scheduled-messages/123/send/"
```

**Hủy message**:
```bash
curl -X PATCH "http://localhost:8000/api/scheduled-messages/123/" \
  -H "Content-Type: application/json" \
  -d '{"action": "cancel"}'
```

### 3. Test hệ thống
```bash
cd django
python test_line_scheduler.py
```

## Monitoring và Troubleshooting

### 1. Check Celery tasks
- Xem Celery worker logs để theo dõi task execution
- Check Redis để xem task queue status

### 2. Check database
```sql
-- Messages đến hạn
SELECT * FROM line_message_setting 
WHERE delivery_datetime <= NOW() 
AND status = '配信予定' 
AND deleted_at IS NULL;

-- Messages có lỗi
SELECT * FROM line_message_setting 
WHERE status = 'エラー' 
AND deleted_at IS NULL;
```

### 3. Logs
- Celery logs: `logs/celery.log`
- Django logs: Console output

## Trạng thái Messages

- **配信予定**: Đã lên lịch, chờ gửi
- **配信中**: Đang trong quá trình gửi
- **配信完了**: Gửi thành công tất cả recipients
- **一部配信完了**: Gửi thành công một phần recipients
- **エラー**: Có lỗi xảy ra
- **キャンセル**: Đã bị hủy

## Lưu ý quan trọng

1. **LINE API Integration**: Hiện tại code chỉ log message thay vì gửi thật. Cần cấu hình LINE_ACCESS_TOKEN và uncomment code trong `send_to_line_api()`

2. **Customer Code Mapping**: Cần mapping từ customer code sang LINE user ID thực tế

3. **Error Handling**: Hệ thống có retry mechanism, nhưng cần monitor và handle các lỗi đặc biệt

4. **Performance**: Với số lượng message lớn, có thể cần optimize batch processing

5. **Security**: Cần thêm authentication cho API endpoints trong production
